'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Clock,
  TrendingUp,
  Calendar,
  Bell,
  Search,
  Plus,
  Settings,
  ChevronRight,
  Target,
  Award,
  Users,
  BarChart3,
  CheckCircle,
  XCircle,
  GripVertical,
  Zap,
  Timer,
} from 'lucide-react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { AnalyticsDashboard } from '../AnalyticsDashboard';
import { CourseDiscovery } from '../course/CourseDiscovery';
import { LearningPathProgressWidget } from './LearningPathProgressWidget';
import { GoalsProgressWidget } from './GoalsProgressWidget';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/hooks/useAuth';

// Define interfaces for type safety
interface Widget {
  id: string;
  x: number;
  y: number;
  w: number;
  h: number;
  component: string;
}

interface StatCardProps {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  value: string | number;
  change?: number;
  color: string;
}

interface DashboardData {
  stats: {
    totalCourses: number;
    completedCourses: number;
    hoursStudied: number;
    avgScore: number;
  };
  progressData: { month: string; hours: number; score: number }[];
  courseData: { name: string; completed: number; total: number }[];
  upcomingDeadlines: { id: number; title: string; date: string; priority: string }[];
  recentActivity: { id: number; type: string; title: string; time: string }[];
}

export const MedicalEducationDashboard = () => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [notifications, setNotifications] = useState([]);
  const [draggedWidget, setDraggedWidget] = useState<string | null>(null);
  const [widgets, setWidgets] = useState<Widget[]>([
    { id: 'progress', x: 0, y: 0, w: 2, h: 1, component: 'ProgressChart' },
    { id: 'schedule', x: 2, y: 0, w: 1, h: 1, component: 'StudySchedule' },
    { id: 'learning-paths', x: 0, y: 1, w: 1, h: 1, component: 'LearningPathProgressWidget' },
    { id: 'goals', x: 1, y: 1, w: 1, h: 1, component: 'GoalsProgressWidget' },
    { id: 'completion', x: 2, y: 1, w: 1, h: 1, component: 'CourseCompletion' },
    { id: 'deadlines', x: 0, y: 2, w: 3, h: 1, component: 'UpcomingDeadlines' },
  ]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // Mock data
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    stats: {
      totalCourses: 12,
      completedCourses: 8,
      hoursStudied: 145,
      avgScore: 87,
    },
    progressData: [
      { month: 'Jan', hours: 20, score: 85 },
      { month: 'Feb', hours: 25, score: 88 },
      { month: 'Mar', hours: 30, score: 87 },
      { month: 'Apr', hours: 35, score: 90 },
      { month: 'May', hours: 28, score: 89 },
      { month: 'Jun', hours: 32, score: 92 },
    ],
    courseData: [
      { name: 'Anatomy', completed: 95, total: 100 },
      { name: 'Physiology', completed: 78, total: 100 },
      { name: 'Pathology', completed: 85, total: 100 },
      { name: 'Pharmacology', completed: 62, total: 100 },
    ],
    upcomingDeadlines: [
      { id: 1, title: 'Cardiology Quiz', date: '2025-06-10', priority: 'high' },
      { id: 2, title: 'Anatomy Assignment', date: '2025-06-12', priority: 'medium' },
      { id: 3, title: 'Clinical Case Study', date: '2025-06-15', priority: 'low' },
    ],
    recentActivity: [
      {
        id: 1,
        type: 'completion',
        title: 'Completed Respiratory System Module',
        time: '2 hours ago',
      },
      { id: 2, type: 'quiz', title: 'Scored 94% on Cardiovascular Quiz', time: '1 day ago' },
      { id: 3, type: 'study', title: 'Studied for 3 hours', time: '2 days ago' },
    ],
  });

  // Simulate API fetch
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setLoading(false);
      } catch (err) {
        setError('Failed to load dashboard data');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Real-time updates simulation
  useEffect(() => {
    const interval = setInterval(() => {
      setDashboardData(prev => ({
        ...prev,
        stats: {
          ...prev.stats,
          hoursStudied: prev.stats.hoursStudied + Math.floor(Math.random() * 2),
        },
      }));
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, widgetId: string) => {
    setDraggedWidget(widgetId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, targetId: string) => {
    e.preventDefault();
    if (draggedWidget && draggedWidget !== targetId) {
      setWidgets(prev => {
        const newWidgets = [...prev];
        const draggedIndex = newWidgets.findIndex(w => w.id === draggedWidget);
        const targetIndex = newWidgets.findIndex(w => w.id === targetId);

        if (draggedIndex !== -1 && targetIndex !== -1) {
          [newWidgets[draggedIndex], newWidgets[targetIndex]] = [
            newWidgets[targetIndex],
            newWidgets[draggedIndex],
          ];
        }
        return newWidgets;
      });
    }
    setDraggedWidget(null);
  };

  const StatCard = ({ icon: Icon, title, value, change, color }: StatCardProps) => (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {change && (
            <p className={`text-sm mt-1 ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change >= 0 ? '+' : ''}
              {change}% from last month
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </div>
  );

  const ProgressChart = () => (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Study Progress</h3>
        <div className="flex space-x-2">
          <button
            className="p-2 hover:bg-gray-100 rounded-lg"
            aria-label="Configure study progress chart"
          >
            <Settings className="w-4 h-4 text-gray-500" />
          </button>
        </div>
      </div>
      <ResponsiveContainer width="100%" height={200}>
        <AreaChart data={dashboardData.progressData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip />
          <Area
            type="monotone"
            dataKey="hours"
            stackId="1"
            stroke="#3B82F6"
            fill="#3B82F6"
            fillOpacity={0.6}
          />
          <Area
            type="monotone"
            dataKey="score"
            stackId="2"
            stroke="#10B981"
            fill="#10B981"
            fillOpacity={0.6}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );

  const CourseCompletion = () => (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Course Progress</h3>
      <div className="space-y-4">
        {dashboardData.courseData.map(course => (
          <div key={course.name}>
            <div className="flex justify-between text-sm mb-1">
              <span className="font-medium text-gray-700">{course.name}</span>
              <span className="text-gray-500">{course.completed}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="progress-bar"
                style={{ '--progress-width': `${course.completed}%` } as React.CSSProperties}
              ></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const UpcomingDeadlines = () => (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Upcoming Deadlines</h3>
      <div className="space-y-3">
        {dashboardData.upcomingDeadlines.map(deadline => (
          <div
            key={deadline.id}
            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div>
              <p className="font-medium text-gray-900">{deadline.title}</p>
              <p className="text-sm text-gray-500">{deadline.date}</p>
            </div>
            <div
              className={`px-2 py-1 rounded-full text-xs font-medium ${
                deadline.priority === 'high'
                  ? 'bg-red-100 text-red-800'
                  : deadline.priority === 'medium'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-green-100 text-green-800'
              }`}
            >
              {deadline.priority}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const StudySchedule = () => (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Schedule</h3>
      <div className="space-y-3">
        <div className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <div>
            <p className="font-medium text-gray-900">Anatomy Review</p>
            <p className="text-sm text-gray-500">9:00 AM - 10:30 AM</p>
          </div>
        </div>
        <div className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <div>
            <p className="font-medium text-gray-900">Clinical Skills</p>
            <p className="text-sm text-gray-500">2:00 PM - 4:00 PM</p>
          </div>
        </div>
        <div className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg">
          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
          <div>
            <p className="font-medium text-gray-900">Study Group</p>
            <p className="text-sm text-gray-500">7:00 PM - 9:00 PM</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderWidget = (widget: Widget) => {
    const components: { [key: string]: () => JSX.Element } = {
      ProgressChart,
      CourseCompletion,
      UpcomingDeadlines,
      StudySchedule,
      LearningPathProgressWidget,
      GoalsProgressWidget,
    };

    const Component = components[widget.component];
    return Component ? <Component /> : null;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-blue-600 rounded-full animate-pulse"></div>
          <div className="w-4 h-4 bg-blue-600 rounded-full animate-pulse delay-75"></div>
          <div className="w-4 h-4 bg-blue-600 rounded-full animate-pulse delay-150"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-6 bg-white rounded-xl shadow-sm border border-red-200">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 font-medium">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Medical Education Dashboard</h1>
            <p className="text-gray-600">Track your learning progress and stay organized</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search courses, assignments..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="relative">
              <label htmlFor="course-filter" className="sr-only">
                Filter courses
              </label>
              <select
                id="course-filter"
                value={selectedFilter}
                onChange={e => setSelectedFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Courses</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
              </select>
            </div>
            <button
              className="relative p-2 text-gray-600 hover:text-gray-900"
              aria-label="View notifications"
            >
              <Bell className="w-5 h-5" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
            </button>
          </div>
        </div>
      </header>

      <div className="flex">
        <aside className="w-64 bg-white border-r border-gray-200 min-h-screen p-6">
          <nav className="space-y-2">
            <a
              href="#"
              className="flex items-center space-x-3 px-3 py-2 text-blue-600 bg-blue-50 rounded-lg"
            >
              <BarChart3 className="w-5 h-5" />
              <span className="font-medium">Dashboard</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg"
            >
              <BookOpen className="w-5 h-5" />
              <span>My Courses</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg"
            >
              <Calendar className="w-5 h-5" />
              <span>Schedule</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg"
            >
              <Target className="w-5 h-5" />
              <span>Goals</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg"
            >
              <Award className="w-5 h-5" />
              <span>Achievements</span>
            </a>
            <a
              href="#"
              className="flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg"
            >
              <Users className="w-5 h-5" />
              <span>Study Groups</span>
            </a>
          </nav>

          <div className="mt-8">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Quick Actions</h3>
            <div className="space-y-2">
              <button
                onClick={() => router.push('/rapid-review')}
                className="w-full flex items-center space-x-2 px-3 py-2 text-left bg-yellow-50 text-yellow-800 hover:bg-yellow-100 rounded-lg border border-yellow-200"
              >
                <Zap className="w-4 h-4" />
                <span className="font-medium">Rapid Review</span>
                <Timer className="w-3 h-3 ml-auto" />
              </button>
              <button className="w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg">
                <Plus className="w-4 h-4" />
                <span>Add Course</span>
              </button>
              <button className="w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg">
                <Calendar className="w-4 h-4" />
                <span>Schedule Study</span>
              </button>
              <button className="w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg">
                <Target className="w-4 h-4" />
                <span>Set Goal</span>
              </button>
            </div>
          </div>
        </aside>

        <main className="flex-1 p-6">
          {/* --- Analytics Overview Section --- */}
          <section className="mb-8">
            <h2 className="text-xl font-bold mb-4">Analytics Overview</h2>
            <AnalyticsDashboard />
          </section>

          {/* --- NEW: Rapid Review Button --- */}
          <div className="mb-8 flex justify-end">
            <Button
              onClick={() => router.push('/rapid-review')}
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold shadow"
            >
              Start Rapid Review
            </Button>
          </div>
          {/* --- END NEW --- */}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <StatCard
              icon={BookOpen}
              title="Total Courses"
              value={dashboardData.stats.totalCourses}
              change={8}
              color="bg-blue-500"
            />
            <StatCard
              icon={CheckCircle}
              title="Completed"
              value={dashboardData.stats.completedCourses}
              change={12}
              color="bg-green-500"
            />
            <StatCard
              icon={Clock}
              title="Hours Studied"
              value={dashboardData.stats.hoursStudied}
              change={15}
              color="bg-purple-500"
            />
            <StatCard
              icon={TrendingUp}
              title="Average Score"
              value={`${dashboardData.stats.avgScore}%`}
              change={5}
              color="bg-orange-500"
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            {widgets.map(widget => (
              <div
                key={widget.id}
                draggable
                onDragStart={e => handleDragStart(e, widget.id)}
                onDragOver={handleDragOver}
                onDrop={e => handleDrop(e, widget.id)}
                className="cursor-move relative group"
              >
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <GripVertical className="w-4 h-4 text-gray-400" />
                </div>
                {renderWidget(widget)}
              </div>
            ))}
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
              <button className="text-blue-600 hover:text-blue-700 font-medium">View All</button>
            </div>
            <div className="space-y-4">
              {dashboardData.recentActivity.map(activity => (
                <div
                  key={activity.id}
                  className="flex items-center space-x-4 p-4 hover:bg-gray-50 rounded-lg"
                >
                  <div
                    className={`p-2 rounded-full ${
                      activity.type === 'completion'
                        ? 'bg-green-100'
                        : activity.type === 'quiz'
                          ? 'bg-blue-100'
                          : 'bg-purple-100'
                    }`}
                  >
                    {activity.type === 'completion' ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : activity.type === 'quiz' ? (
                      <Award className="w-4 h-4 text-blue-600" />
                    ) : (
                      <BookOpen className="w-4 h-4 text-purple-600" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{activity.title}</p>
                    <p className="text-sm text-gray-500">{activity.time}</p>
                  </div>
                  <ChevronRight className="w-4 h-4 text-gray-400" />
                </div>
              ))}
            </div>
          </div>

          {/* Course Discovery Section */}
          <div className="mt-8">
            <CourseDiscovery userId={user?.id} />
          </div>
        </main>
      </div>
    </div>
  );
};
