<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200px" height="630px" viewBox="0 0 1200 630" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>MedTrack Hub Open Graph Image</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="ogGradient">
            <stop stop-color="#4F46E5" offset="0%"></stop>
            <stop stop-color="#2563EB" offset="50%"></stop>
            <stop stop-color="#1E40AF" offset="100%"></stop>
        </linearGradient>
        <filter id="textShadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.3"/>
        </filter>
    </defs>
    <g id="OG-Image" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background -->
        <rect fill="url(#ogGradient)" x="0" y="0" width="1200" height="630"></rect>
        
        <!-- Background Pattern -->
        <g id="Background-Pattern" transform="translate(0, 0)" fill="#FFFFFF" opacity="0.1">
            <circle cx="100" cy="100" r="30"></circle>
            <circle cx="300" cy="150" r="20"></circle>
            <circle cx="500" cy="80" r="25"></circle>
            <circle cx="700" cy="120" r="15"></circle>
            <circle cx="900" cy="90" r="35"></circle>
            <circle cx="1100" cy="140" r="20"></circle>
            
            <circle cx="150" cy="400" r="25"></circle>
            <circle cx="350" cy="450" r="30"></circle>
            <circle cx="550" cy="380" r="20"></circle>
            <circle cx="750" cy="420" r="25"></circle>
            <circle cx="950" cy="390" r="15"></circle>
            <circle cx="1150" cy="440" r="30"></circle>
        </g>
        
        <!-- Main Content -->
        <g id="Content" transform="translate(100, 150)">
            <!-- Logo -->
            <g id="Logo" transform="translate(0, 0)">
                <rect fill="#FFFFFF" x="0" y="0" width="80" height="80" rx="16"></rect>
                <g id="Medical-Cross" transform="translate(20, 20)" fill="#4F46E5">
                    <path d="M15,0 L25,0 L25,15 L40,15 L40,25 L25,25 L25,40 L15,40 L15,25 L0,25 L0,15 L15,15 L15,0 Z"></path>
                </g>
            </g>
            
            <!-- Title -->
            <text id="Title" font-family="Arial-BoldMT, Arial" font-size="72" font-weight="bold" fill="#FFFFFF" filter="url(#textShadow)">
                <tspan x="120" y="60">MedTrack Hub</tspan>
            </text>
            
            <!-- Subtitle -->
            <text id="Subtitle" font-family="Arial, sans-serif" font-size="36" font-weight="normal" fill="#E5E7EB">
                <tspan x="120" y="120">Medical Learning Platform</tspan>
            </text>
            
            <!-- Description -->
            <text id="Description" font-family="Arial, sans-serif" font-size="24" font-weight="normal" fill="#D1D5DB">
                <tspan x="120" y="170">Track your medical study progress,</tspan>
                <tspan x="120" y="210">assess your readiness, and stay ahead.</tspan>
            </text>
        </g>
        
        <!-- Medical Icons -->
        <g id="Medical-Icons" transform="translate(800, 200)" fill="#FFFFFF" opacity="0.3">
            <!-- Stethoscope -->
            <g id="Stethoscope" transform="translate(0, 0)" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" fill="none">
                <path d="M40,80 C40,91.045695 31.045695,100 20,100 C8.954305,100 0,91.045695 0,80 C0,68.954305 8.954305,60 20,60"></path>
                <path d="M40,20 L40,80"></path>
                <path d="M20,60 L20,20"></path>
                <path d="M20,20 C20,8.954305 28.954305,0 40,0 C51.045695,0 60,8.954305 60,20 C60,31.045695 51.045695,40 40,40 L40,20"></path>
            </g>
            
            <!-- Pills -->
            <g id="Pills" transform="translate(120, 20)">
                <ellipse cx="20" cy="10" rx="15" ry="8" fill="#FFFFFF"></ellipse>
                <ellipse cx="50" cy="25" rx="12" ry="6" fill="#FFFFFF"></ellipse>
                <ellipse cx="80" cy="15" rx="18" ry="9" fill="#FFFFFF"></ellipse>
            </g>
            
            <!-- Heart -->
            <g id="Heart" transform="translate(150, 80)">
                <path d="M20,30 C20,25 25,20 30,20 C35,20 40,25 40,30 C40,40 20,55 20,55 C20,55 0,40 0,30 C0,25 5,20 10,20 C15,20 20,25 20,30 Z" fill="#FFFFFF"></path>
            </g>
        </g>
    </g>
</svg>
