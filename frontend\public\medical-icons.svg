<?xml version="1.0" encoding="UTF-8"?>
<svg width="400px" height="400px" viewBox="0 0 400 400" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Medical Icons Collection</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="medicalGradient">
            <stop stop-color="#4F46E5" offset="0%"></stop>
            <stop stop-color="#2563EB" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Medical-Icons-Collection" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect fill="#F9FAFB" x="0" y="0" width="400" height="400"></rect>
        
        <!-- Stethoscope Icon -->
        <g id="Stethoscope" transform="translate(50, 50)" stroke="#4F46E5" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" fill="none">
            <path d="M40,60 C40,71.045695 31.045695,80 20,80 C8.954305,80 0,71.045695 0,60 C0,48.954305 8.954305,40 20,40"></path>
            <path d="M40,20 L40,60"></path>
            <path d="M20,40 L20,20"></path>
            <path d="M20,20 C20,8.954305 28.954305,0 40,0 C51.045695,0 60,8.954305 60,20 C60,31.045695 51.045695,40 40,40 L40,20"></path>
        </g>
        
        <!-- Heart Icon -->
        <g id="Heart" transform="translate(200, 50)" fill="#E11D48">
            <path d="M30,20 C30,15 35,10 40,10 C45,10 50,15 50,20 C50,30 30,45 30,45 C30,45 10,30 10,20 C10,15 15,10 20,10 C25,10 30,15 30,20 Z"></path>
        </g>
        
        <!-- Pills Icon -->
        <g id="Pills" transform="translate(50, 150)" fill="#10B981">
            <ellipse cx="20" cy="15" rx="15" ry="10"></ellipse>
            <ellipse cx="50" cy="25" rx="12" ry="8"></ellipse>
            <ellipse cx="80" cy="20" rx="18" ry="12"></ellipse>
        </g>
        
        <!-- Medical Cross -->
        <g id="Medical-Cross" transform="translate(200, 150)" fill="#4F46E5">
            <path d="M20,0 L40,0 L40,20 L60,20 L60,40 L40,40 L40,60 L20,60 L20,40 L0,40 L0,20 L20,20 L20,0 Z"></path>
        </g>
        
        <!-- Syringe Icon -->
        <g id="Syringe" transform="translate(50, 250)" stroke="#F59E0B" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" fill="none">
            <path d="M10,10 L50,50"></path>
            <path d="M15,5 L20,10 L10,20 L5,15 L15,5 Z"></path>
            <path d="M45,45 L55,55"></path>
            <path d="M50,40 L60,50"></path>
        </g>
        
        <!-- Brain Icon -->
        <g id="Brain" transform="translate(200, 250)" fill="#8B5CF6">
            <path d="M30,10 C40,10 50,15 50,25 C50,30 45,35 40,35 C45,40 40,45 35,45 C30,50 20,50 15,45 C10,45 5,40 10,35 C5,35 0,30 0,25 C0,15 10,10 20,10 C25,10 30,10 30,10 Z"></path>
        </g>
        
        <!-- Microscope Icon -->
        <g id="Microscope" transform="translate(300, 50)" stroke="#6366F1" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" fill="none">
            <path d="M20,10 L20,40"></path>
            <path d="M15,40 L25,40"></path>
            <path d="M10,50 L30,50"></path>
            <circle cx="20" cy="15" r="5"></circle>
            <circle cx="20" cy="25" r="3"></circle>
        </g>
        
        <!-- DNA Helix -->
        <g id="DNA" transform="translate(300, 150)" stroke="#EC4899" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" fill="none">
            <path d="M10,0 Q20,10 10,20 Q0,30 10,40 Q20,50 10,60"></path>
            <path d="M30,0 Q20,10 30,20 Q40,30 30,40 Q20,50 30,60"></path>
            <path d="M10,15 L30,15"></path>
            <path d="M10,30 L30,30"></path>
            <path d="M10,45 L30,45"></path>
        </g>
        
        <!-- Medical Bag -->
        <g id="Medical-Bag" transform="translate(300, 250)" stroke="#059669" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" fill="#10B981" fill-opacity="0.2">
            <rect x="5" y="20" width="50" height="30" rx="5"></rect>
            <path d="M15,20 L15,15 C15,10 20,5 25,5 L35,5 C40,5 45,10 45,15 L45,20"></path>
            <path d="M25,30 L35,30"></path>
            <path d="M30,25 L30,35"></path>
        </g>
    </g>
</svg>
