import 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role?: string;
      accessToken?: string;
      specialization?: string;
      year?: string;
      institution?: string;
      bio?: string;
    };
  }

  interface User {
    id: string;
    name: string;
    email: string;
    image?: string;
    role: string;
    accessToken: string;
    specialization?: string;
    year?: string;
    institution?: string;
    bio?: string;
  }
}