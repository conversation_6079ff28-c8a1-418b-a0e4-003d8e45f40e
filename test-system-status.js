const { Client } = require('pg');
const http = require('http');

async function testSystemStatus() {
  console.log('='.repeat(60));
  console.log('🔍 MEDICAL LEARNING SYSTEM - STATUS CHECK');
  console.log('='.repeat(60));

  // Test 1: Database Connection
  console.log('\n1️⃣ Testing Database Connection...');
  const client = new Client({
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT) || 5432,
    user: process.env.POSTGRES_USER || 'medical',
    password: process.env.POSTGRES_PASSWORD || 'AU110s/6081/2021MT',
    database: process.env.POSTGRES_DB || 'medical_tracker',
  });

  try {
    await client.connect();
    console.log('✅ Database connection: SUCCESS');

    // Check tables
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `);
    
    console.log(`📊 Found ${tablesResult.rows.length} tables:`);
    tablesResult.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });

    // Check for specific tables
    const requiredTables = ['users', 'learning_paths', 'learning_goals'];
    const existingTables = tablesResult.rows.map(r => r.table_name);
    
    console.log('\n📋 Required Tables Status:');
    requiredTables.forEach(table => {
      const exists = existingTables.includes(table);
      console.log(`   ${exists ? '✅' : '❌'} ${table}: ${exists ? 'EXISTS' : 'MISSING'}`);
    });

    // Count records if tables exist
    if (existingTables.includes('users')) {
      const userCount = await client.query('SELECT COUNT(*) FROM users');
      console.log(`👥 Users: ${userCount.rows[0].count}`);
    }

    if (existingTables.includes('learning_paths')) {
      const pathCount = await client.query('SELECT COUNT(*) FROM learning_paths');
      console.log(`🛤️ Learning Paths: ${pathCount.rows[0].count}`);
    }

    if (existingTables.includes('learning_goals')) {
      const goalCount = await client.query('SELECT COUNT(*) FROM learning_goals');
      console.log(`🎯 Learning Goals: ${goalCount.rows[0].count}`);
    }

    await client.end();
  } catch (error) {
    console.log('❌ Database connection: FAILED');
    console.log(`   Error: ${error.message}`);
  }

  // Test 2: Backend API
  console.log('\n2️⃣ Testing Backend API...');
  
  const testEndpoint = (path) => {
    return new Promise((resolve) => {
      const options = {
        hostname: 'localhost',
        port: 3002,
        path: path,
        method: 'GET',
        timeout: 5000
      };

      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          resolve({
            status: res.statusCode,
            data: data.substring(0, 200),
            success: true
          });
        });
      });

      req.on('error', (error) => {
        resolve({
          status: 'ERROR',
          data: error.message,
          success: false
        });
      });

      req.on('timeout', () => {
        req.destroy();
        resolve({
          status: 'TIMEOUT',
          data: 'Request timed out',
          success: false
        });
      });

      req.end();
    });
  };

  const endpoints = [
    { path: '/health', name: 'Health Check' },
    { path: '/learning-paths', name: 'Learning Paths' },
    { path: '/learning-goals', name: 'Learning Goals' }
  ];

  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint.path);
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${endpoint.name}: ${result.status}`);
    if (result.data && result.success) {
      console.log(`   Response: ${result.data}...`);
    } else if (!result.success) {
      console.log(`   Error: ${result.data}`);
    }
  }

  // Test 3: Docker Containers
  console.log('\n3️⃣ Docker Container Status:');
  console.log('   (Check manually with: docker ps)');

  console.log('\n' + '='.repeat(60));
  console.log('📋 SUMMARY');
  console.log('='.repeat(60));
  console.log('✅ Database: Connected and accessible');
  console.log('🔄 Backend: Check API responses above');
  console.log('📊 Tables: Check table status above');
  console.log('🎯 Next: Run seed scripts if tables exist');
  console.log('='.repeat(60));
}

testSystemStatus().catch(console.error);
