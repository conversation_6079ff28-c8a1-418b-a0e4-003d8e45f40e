name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '20'
  PYTHON_VERSION: '3.11'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Test Jobs
  test-frontend:
    name: Test Frontend
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package.json

    - name: Install dependencies
      working-directory: ./frontend
      run: npm install --no-audit --no-fund
      
    - name: Run linting
      working-directory: ./frontend
      run: npm run lint
      
    - name: Run type checking
      working-directory: ./frontend
      run: npm run type-check
      
    - name: Run tests
      working-directory: ./frontend
      run: npm run test:ci
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  test-backend:
    name: Test Backend
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:16-alpine
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: medtrack_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'pnpm'
        cache-dependency-path: backend/pnpm-lock.yaml

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 9.12.3

    - name: Install dependencies
      working-directory: ./backend
      run: pnpm install --frozen-lockfile
      
    - name: Run linting
      working-directory: ./backend
      run: pnpm run lint

    - name: Run unit tests
      working-directory: ./backend
      run: pnpm run test
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/medtrack_test
        JWT_SECRET: test-jwt-secret-for-ci-pipeline-only
        JWT_REFRESH_SECRET: test-jwt-refresh-secret-for-ci-pipeline-only
        REDIS_URL: redis://localhost:6379
        
    - name: Run e2e tests
      working-directory: ./backend
      run: pnpm run test:e2e
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/medtrack_test
        JWT_SECRET: test-jwt-secret-for-ci-pipeline-only
        JWT_REFRESH_SECRET: test-jwt-refresh-secret-for-ci-pipeline-only
        REDIS_URL: redis://localhost:6379
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage/lcov.info
        flags: backend
        name: backend-coverage

  test-analytics:
    name: Test Analytics
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:16-alpine
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: medtrack_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
        cache-dependency-path: backend/python_analytics/requirements.txt
        
    - name: Install dependencies
      working-directory: ./backend/python_analytics
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov flake8 black
        
    - name: Run linting
      working-directory: ./backend/python_analytics
      run: flake8 .
      
    - name: Check code formatting
      working-directory: ./backend/python_analytics
      run: black --check .
      
    - name: Run tests
      working-directory: ./backend/python_analytics
      run: pytest --cov=analytics --cov-report=xml
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/medtrack_test
        JWT_SECRET: test-jwt-secret-for-ci-pipeline-only
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/python_analytics/coverage.xml
        flags: analytics
        name: analytics-coverage

  # Security Scanning
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Build and Push Docker Images
  build-and-push:
    name: Build and Push Images
    runs-on: ubuntu-latest
    needs: [test-frontend, test-backend, test-analytics]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    permissions:
      contents: read
      packages: write
      
    strategy:
      matrix:
        service: [frontend, backend, analytics]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ${{ matrix.service == 'analytics' && './backend/python_analytics' || format('./{0}', matrix.service) }}
        file: ${{ matrix.service == 'analytics' && './backend/python_analytics/Dockerfile' || format('./{0}/Dockerfile', matrix.service) }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        # Example: kubectl apply -f k8s/staging/
        
    - name: Run health checks
      run: |
        echo "Running health checks..."
        # Add health check commands here

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
        # Example: kubectl apply -f k8s/production/
        
    - name: Run health checks
      run: |
        echo "Running health checks..."
        # Add health check commands here
        
    - name: Notify deployment
      if: always()
      run: |
        echo "Notifying team about deployment status..."
        # Add notification commands here (Slack, email, etc.)
