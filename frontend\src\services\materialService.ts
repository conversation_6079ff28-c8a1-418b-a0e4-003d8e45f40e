import { apiService } from './api';

export interface Material {
  id: string;
  title: string;
  description?: string;
  type?: string;
  author?: string;
  uploadDate?: string;
  size?: string;
  url?: string;
}

// Mock data for development
const mockMaterials: Material[] = [
  {
    id: '1',
    title: 'Anatomy Fundamentals',
    description: 'Basic anatomy concepts and structures',
    type: 'PDF',
    author: '<PERSON><PERSON>',
    uploadDate: '2024-01-15',
    size: '2.5 MB',
    url: '/materials/anatomy-fundamentals.pdf'
  },
  {
    id: '2',
    title: 'Pharmacology Basics',
    description: 'Introduction to pharmacological principles',
    type: 'Video',
    author: '<PERSON><PERSON> <PERSON>',
    uploadDate: '2024-01-20',
    size: '150 MB',
    url: '/materials/pharmacology-basics.mp4'
  }
];

const materialService = {
  async getMaterials(): Promise<Material[]> {
    try {
      const response = await apiService.get('/materials');
      return response.data;
    } catch (error) {
      console.warn('Error fetching materials from API, using mock data:', error);
      // Return mock data as fallback
      return mockMaterials;
    }
  },

  async getMaterialById(id: string): Promise<Material> {
    try {
      const response = await apiService.get(`/materials/${id}`);
      return response.data;
    } catch (error) {
      console.warn('Error fetching material by ID from API, using mock data:', error);
      // Return mock data as fallback
      const material = mockMaterials.find(m => m.id === id);
      if (material) {
        return material;
      }
      throw new Error(`Material with id ${id} not found`);
    }
  },

  async uploadMaterial(formData: FormData): Promise<Material> {
    try {
      const response = await apiService.post('/materials/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.warn('Error uploading material to API, using mock data:', error);
      // Create mock material from form data
      const title = formData.get('title') as string || 'Untitled';
      const description = formData.get('description') as string || '';
      const file = formData.get('file') as File;
      const type = file?.name.split('.').pop() || 'unknown';

      const newMaterial: Material = {
        id: Date.now().toString(),
        title,
        description,
        type,
        author: 'Current User',
        uploadDate: new Date().toISOString().split('T')[0],
        size: file ? `${(file.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown',
        url: `/materials/${title.toLowerCase().replace(/\s+/g, '-')}.${type}`
      };

      return newMaterial;
    }
  },

  async deleteMaterial(id: string): Promise<void> {
    try {
      await apiService.delete(`/materials/${id}`);
    } catch (error) {
      console.warn('Error deleting material from API:', error);
      // Mock deletion - just log it
      console.log('Mock delete material:', id);
    }
  },

  async shareMaterial(id: string, userIds: string[]): Promise<void> {
    try {
      await apiService.post('/materials/share', { materialId: id, userIds });
    } catch (error) {
      console.warn('Error sharing material via API:', error);
      // Mock sharing - just log it
      console.log('Mock share material:', { id, userIds });
    }
  },
};

export default materialService;
