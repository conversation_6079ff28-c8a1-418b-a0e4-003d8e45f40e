import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { LearningPathsService, CreateLearningPathDto, UpdateLearningPathDto, LearningPathFilters } from './learning-paths.service';
import { LearningPathProgressService, StartLearningPathDto, UpdateProgressDto } from './learning-path-progress.service';
import { LearningPathRecommendationsService } from './learning-path-recommendations.service';
import { LearningPathAnalyticsService } from './learning-path-analytics.service';
import { LearningPathCategory, LearningPathType } from '../../entities/learning-path.entity';

@ApiTags('learning-paths')
@Controller('learning-paths')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class LearningPathsController {
  constructor(
    private readonly learningPathsService: LearningPathsService,
    private readonly progressService: LearningPathProgressService,
    private readonly recommendationsService: LearningPathRecommendationsService,
    private readonly analyticsService: LearningPathAnalyticsService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new learning path' })
  @ApiResponse({ status: 201, description: 'Learning path created successfully' })
  async create(@Body() createDto: CreateLearningPathDto, @Request() req) {
    return await this.learningPathsService.create(createDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all learning paths with optional filters' })
  @ApiResponse({ status: 200, description: 'Learning paths retrieved successfully' })
  async findAll(@Query() filters: LearningPathFilters) {
    return await this.learningPathsService.findAll(filters);
  }

  @Get('recommendations')
  @ApiOperation({ summary: 'Get personalized learning path recommendations' })
  @ApiResponse({ status: 200, description: 'Recommendations retrieved successfully' })
  async getRecommendations(@Request() req, @Query('limit') limit?: number) {
    return await this.recommendationsService.getRecommendations(req.user.id, limit);
  }

  @Get('recommendations/collaborative')
  @ApiOperation({ summary: 'Get collaborative filtering recommendations' })
  @ApiResponse({ status: 200, description: 'Collaborative recommendations retrieved successfully' })
  async getCollaborativeRecommendations(@Request() req, @Query('limit') limit?: number) {
    return await this.recommendationsService.getCollaborativeRecommendations(req.user.id, limit);
  }

  @Get('trending')
  @ApiOperation({ summary: 'Get trending learning paths' })
  @ApiResponse({ status: 200, description: 'Trending paths retrieved successfully' })
  async getTrending(@Query('limit') limit?: number) {
    return await this.recommendationsService.getTrendingPaths(limit);
  }

  @Get('popular')
  @ApiOperation({ summary: 'Get popular learning paths' })
  @ApiResponse({ status: 200, description: 'Popular paths retrieved successfully' })
  async getPopular(@Query('limit') limit?: number) {
    return await this.learningPathsService.getPopular(limit);
  }

  @Get('categories/:category')
  @ApiOperation({ summary: 'Get learning paths by category' })
  @ApiResponse({ status: 200, description: 'Category paths retrieved successfully' })
  async getByCategory(@Param('category') category: LearningPathCategory) {
    return await this.learningPathsService.getByCategory(category);
  }

  @Get('my-progress')
  @ApiOperation({ summary: 'Get user\'s learning path progress' })
  @ApiResponse({ status: 200, description: 'User progress retrieved successfully' })
  async getMyProgress(@Request() req) {
    return await this.progressService.getUserProgress(req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a learning path by ID' })
  @ApiResponse({ status: 200, description: 'Learning path retrieved successfully' })
  async findOne(@Param('id') id: string) {
    return await this.learningPathsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a learning path' })
  @ApiResponse({ status: 200, description: 'Learning path updated successfully' })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateLearningPathDto,
    @Request() req,
  ) {
    return await this.learningPathsService.update(id, updateDto, req.user.id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a learning path' })
  @ApiResponse({ status: 204, description: 'Learning path deleted successfully' })
  async remove(@Param('id') id: string, @Request() req) {
    await this.learningPathsService.remove(id, req.user.id);
  }

  @Post(':id/duplicate')
  @ApiOperation({ summary: 'Duplicate a learning path' })
  @ApiResponse({ status: 201, description: 'Learning path duplicated successfully' })
  async duplicate(
    @Param('id') id: string,
    @Body() customizations: Partial<CreateLearningPathDto>,
    @Request() req,
  ) {
    return await this.learningPathsService.duplicate(id, req.user.id, customizations);
  }

  @Post(':id/enroll')
  @ApiOperation({ summary: 'Enroll in a learning path' })
  @ApiResponse({ status: 201, description: 'Successfully enrolled in learning path' })
  async enroll(
    @Param('id') id: string,
    @Body() startDto: Omit<StartLearningPathDto, 'learning_path_id'>,
    @Request() req,
  ) {
    const enrollDto: StartLearningPathDto = {
      learning_path_id: id,
      ...startDto,
    };
    return await this.progressService.startLearningPath(req.user.id, enrollDto);
  }

  @Get(':id/progress')
  @ApiOperation({ summary: 'Get progress for a specific learning path' })
  @ApiResponse({ status: 200, description: 'Progress retrieved successfully' })
  async getProgress(@Param('id') id: string, @Request() req) {
    return await this.progressService.getProgress(req.user.id, id);
  }

  @Patch(':id/progress')
  @ApiOperation({ summary: 'Update progress for a learning path' })
  @ApiResponse({ status: 200, description: 'Progress updated successfully' })
  async updateProgress(
    @Param('id') id: string,
    @Body() updateDto: UpdateProgressDto,
    @Request() req,
  ) {
    return await this.progressService.updateProgress(req.user.id, id, updateDto);
  }

  @Get(':id/analytics')
  @ApiOperation({ summary: 'Get detailed analytics for learning path progress' })
  @ApiResponse({ status: 200, description: 'Analytics retrieved successfully' })
  async getProgressAnalytics(@Param('id') id: string, @Request() req) {
    return await this.progressService.getProgressAnalytics(req.user.id, id);
  }

  @Post(':id/pause')
  @ApiOperation({ summary: 'Pause learning path progress' })
  @ApiResponse({ status: 200, description: 'Learning path paused successfully' })
  async pauseLearningPath(@Param('id') id: string, @Request() req) {
    return await this.progressService.pauseLearningPath(req.user.id, id);
  }

  @Post(':id/resume')
  @ApiOperation({ summary: 'Resume learning path progress' })
  @ApiResponse({ status: 200, description: 'Learning path resumed successfully' })
  async resumeLearningPath(@Param('id') id: string, @Request() req) {
    return await this.progressService.resumeLearningPath(req.user.id, id);
  }

  @Post(':id/complete')
  @ApiOperation({ summary: 'Mark learning path as completed' })
  @ApiResponse({ status: 200, description: 'Learning path completed successfully' })
  async completeLearningPath(@Param('id') id: string, @Request() req) {
    return await this.progressService.completeLearningPath(req.user.id, id);
  }

  @Get(':id/analytics')
  @ApiOperation({ summary: 'Get analytics for a specific learning path' })
  @ApiResponse({ status: 200, description: 'Path analytics retrieved successfully' })
  async getPathAnalytics(@Param('id') id: string) {
    return await this.analyticsService.getPathAnalytics(id);
  }

  @Get('analytics/system')
  @ApiOperation({ summary: 'Get system-wide learning path analytics' })
  @ApiResponse({ status: 200, description: 'System analytics retrieved successfully' })
  async getSystemAnalytics() {
    return await this.analyticsService.getSystemAnalytics();
  }

  @Get('analytics/user-insights')
  @ApiOperation({ summary: 'Get personalized user insights' })
  @ApiResponse({ status: 200, description: 'User insights retrieved successfully' })
  async getUserInsights(@Request() req) {
    return await this.analyticsService.getUserInsights(req.user.id);
  }
}
