import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token;
    const isAuth = !!token;
    const pathname = req.nextUrl.pathname;

    // Check if it's an auth route
    const isAuthPage = pathname.startsWith('/auth') || pathname.startsWith('/(auth)');

    // Check if it's an app route (protected)
    const isAppPage = pathname.startsWith('/(app)') ||
                     pathname.startsWith('/dashboard') ||
                     pathname.startsWith('/courses') ||
                     pathname.startsWith('/materials') ||
                     pathname.startsWith('/profile') ||
                     pathname.startsWith('/settings');

    if (isAuthPage) {
      if (isAuth) {
        return NextResponse.redirect(new URL('/dashboard', req.url));
      }
      return null;
    }

    if (isAppPage && !isAuth) {
      let from = pathname;
      if (req.nextUrl.search) {
        from += req.nextUrl.search;
      }

      return NextResponse.redirect(
        new URL(`/auth/login?from=${encodeURIComponent(from)}`, req.url)
      );
    }
  },
  {
    callbacks: {
      authorized: ({ req, token }) => {
        const pathname = req.nextUrl.pathname;

        // Public routes that don't require authentication
        if (
          pathname.startsWith('/auth/') ||
          pathname.startsWith('/(auth)/') ||
          pathname.startsWith('/api/auth/') ||
          pathname === '/' ||
          pathname.startsWith('/(marketing)/')
        ) {
          return true;
        }

        // Check if user is authenticated for protected routes
        if (!token) {
          return false;
        }

        // Admin-only routes
        const adminRoutes = ['/settings/admin', '/users/manage', '/(app)/admin'];
        if (adminRoutes.some(route => pathname.startsWith(route))) {
          return token.role === 'admin';
        }

        // Instructor-only routes
        const instructorRoutes = ['/courses/create', '/courses/edit', '/(app)/courses/create'];
        if (instructorRoutes.some(route => pathname.startsWith(route))) {
          return ['admin', 'instructor'].includes(token.role as string);
        }

        // All other protected routes just need authentication
        return true;
      },
    },
  }
);

export const config = {
  matcher: [
    // Protected app routes
    '/(app)/:path*',
    // Auth routes
    '/(auth)/:path*',
    // Legacy routes that might still exist
    '/dashboard/:path*',
    '/materials/:path*',
    '/courses/:path*',
    '/progress/:path*',
    '/schedule/:path*',
    '/quiz/:path*',
    '/upload/:path*',
    '/settings/:path*',
    '/profile/:path*',
    '/notifications/:path*',
    '/chat/:path*',
  ],
};
