import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:3002';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ courseId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.accessToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const { courseId } = resolvedParams;

    const response = await fetch(`${BACKEND_URL}/courses/${courseId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.accessToken}`,
      },
    });

    if (!response.ok) {
      const error = await response.text();
      return NextResponse.json(
        { error: 'Failed to fetch course prerequisites', details: error },
        { status: response.status }
      );
    }

    const course = await response.json();
    
    // Extract prerequisites information
    const prerequisites = {
      course_ids: course.prerequisites?.course_ids || [],
      skills: course.prerequisites?.skills || [],
      description: course.prerequisites?.description || '',
      prerequisite_courses: course.prerequisite_courses || [],
    };

    return NextResponse.json(prerequisites);
  } catch (error) {
    console.error('Prerequisites API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
