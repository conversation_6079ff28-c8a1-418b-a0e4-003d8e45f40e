'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { FaEnvelope, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaTimes } from 'react-icons/fa';

export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'verifying' | 'success' | 'error'>('verifying');
  const [message, setMessage] = useState('Verifying your email...');

  useEffect(() => {
    const verifyEmail = async () => {
      const token = searchParams?.get('token');
      if (!token) {
        setStatus('error');
        setMessage('Invalid verification link');
        return;
      }

      try {
        const response = await fetch(`/api/auth/verify-email/${token}`, {
          method: 'GET',
        });

        const data = await response.json();

        if (response.ok) {
          setStatus('success');
          setMessage(data.message || 'Email verified successfully!');
          // Redirect to login after 3 seconds
          setTimeout(() => {
            router.push('/auth/login');
          }, 3000);
        } else {
          setStatus('error');
          setMessage(data.message || 'Failed to verify email');
        }
      } catch (error) {
        setStatus('error');
        setMessage('An error occurred while verifying your email');
      }
    };

    verifyEmail();
  }, [router, searchParams]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Email Verification
          </h2>
        </div>

        <div className="mt-8 space-y-6">
          <div className="rounded-md bg-white px-6 py-5 shadow-sm">
            <div className="flex flex-col items-center space-y-4">
              <div className="rounded-full bg-gray-100 p-3">
                {status === 'verifying' && (
                  <FaSpinner className="h-6 w-6 text-blue-500 animate-spin" />
                )}
                {status === 'success' && <FaCheck className="h-6 w-6 text-green-500" />}
                {status === 'error' && <FaTimes className="h-6 w-6 text-red-500" />}
              </div>
              <div className="text-center">
                <p
                  className={`text-lg ${
                    status === 'verifying'
                      ? 'text-blue-600'
                      : status === 'success'
                        ? 'text-green-600'
                        : 'text-red-600'
                  }`}
                >
                  {message}
                </p>
              </div>
              {status === 'error' && (
                <div className="text-center space-y-3">
                  <p className="text-sm text-gray-500">
                    If you're having trouble verifying your email, you can:
                  </p>
                  <button
                    onClick={async () => {
                      try {
                        const response = await fetch('/api/auth/email/resend', {
                          method: 'POST',
                        });
                        const data = await response.json();
                        if (response.ok) {
                          setMessage('New verification email sent! Please check your inbox.');
                        } else {
                          setMessage(data.message || 'Failed to resend verification email');
                        }
                      } catch (error) {
                        setMessage('Failed to resend verification email');
                      }
                    }}
                    className="text-blue-600 hover:text-blue-500"
                  >
                    Request a new verification link
                  </button>
                  <div className="pt-2">
                    <Link href="/auth/login" className="text-blue-600 hover:text-blue-500">
                      Return to login
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
