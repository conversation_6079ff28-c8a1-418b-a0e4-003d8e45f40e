"""
TensorFlow-based machine learning models for medical education analytics

This module provides specialized ML models for:
1. Student performance prediction in medical subjects
2. Learning pattern analysis for medical curriculum
3. Knowledge retention modeling for medical concepts
4. Adaptive learning recommendations for medical education
"""
import numpy as np
import pandas as pd
import tensorflow as tf
from typing import Dict, Any, List, Tuple, Optional
import logging
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import os
import pickle
import json
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MedicalEducationPerformanceModel:
    """
    Advanced TensorFlow model for predicting student performance in medical education

    Features:
    - Multi-subject performance prediction (Anatomy, Physiology, Pharmacology, etc.)
    - Temporal learning pattern analysis
    - Knowledge retention modeling
    - Adaptive difficulty adjustment
    """

    def __init__(self, model_path: str = "models/medical_performance_model"):
        self.model_path = model_path
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.is_trained = False
        self.medical_subjects = [
            'anatomy', 'physiology', 'biochemistry', 'pharmacology',
            'pathology', 'microbiology', 'immunology', 'genetics'
        ]
        self.difficulty_levels = ['beginner', 'intermediate', 'advanced', 'expert']
        
    def create_model(self, input_shape: int) -> tf.keras.Model:
        """
        Create an advanced neural network model for medical education performance prediction

        Architecture:
        - Multi-layer perceptron with attention mechanism
        - Specialized for medical subject classification
        - Includes regularization for better generalization
        """
        # Input layer
        inputs = tf.keras.layers.Input(shape=(input_shape,))

        # Feature extraction layers
        x = tf.keras.layers.Dense(256, activation='relu')(inputs)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Dropout(0.3)(x)

        x = tf.keras.layers.Dense(128, activation='relu')(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Dropout(0.2)(x)

        # Attention mechanism for medical subject focus
        attention = tf.keras.layers.Dense(128, activation='softmax')(x)
        x = tf.keras.layers.Multiply()([x, attention])

        x = tf.keras.layers.Dense(64, activation='relu')(x)
        x = tf.keras.layers.Dropout(0.1)(x)

        # Multi-output for different medical subjects
        performance_output = tf.keras.layers.Dense(1, activation='sigmoid', name='performance')(x)
        subject_difficulty = tf.keras.layers.Dense(len(self.difficulty_levels),
                                                 activation='softmax', name='difficulty')(x)

        model = tf.keras.Model(inputs=inputs, outputs=[performance_output, subject_difficulty])

        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss={
                'performance': 'binary_crossentropy',
                'difficulty': 'categorical_crossentropy'
            },
            loss_weights={'performance': 1.0, 'difficulty': 0.3},
            metrics={
                'performance': ['accuracy', 'precision', 'recall'],
                'difficulty': ['accuracy']
            }
        )

        return model
    
    def prepare_medical_features(self, user_data: Dict[str, Any]) -> np.ndarray:
        """
        Prepare comprehensive features for medical education performance prediction

        Features include:
        - Subject-specific performance metrics
        - Learning pattern analysis
        - Knowledge retention indicators
        - Temporal study behavior
        - Medical concept difficulty progression
        """
        try:
            features = []

            # Subject-specific performance features
            subject_performance = user_data.get("subject_performance", {})
            for subject in self.medical_subjects:
                perf = subject_performance.get(subject, {})
                accuracy = perf.get("accuracy", 0.5)
                completion = perf.get("completion_rate", 0.0)
                time_spent = perf.get("total_time", 0.0)
                features.extend([accuracy, completion, time_spent])

            # Learning pattern features
            study_sessions = user_data.get("study_sessions", [])
            if study_sessions:
                # Calculate study consistency and patterns
                session_durations = [s.get("duration", 0) for s in study_sessions]
                avg_session = np.mean(session_durations)
                session_variance = np.var(session_durations)
                study_frequency = len(study_sessions) / 30  # Sessions per day over 30 days

                # Time-of-day preferences
                session_hours = [s.get("hour", 12) for s in study_sessions]
                preferred_hour = np.mean(session_hours) / 24.0  # Normalize to 0-1

                features.extend([avg_session, session_variance, study_frequency, preferred_hour])
            else:
                features.extend([0, 0, 0, 0.5])  # Default values

            # Knowledge retention features
            quiz_history = user_data.get("quiz_history", [])
            if quiz_history:
                # Calculate retention curve
                recent_scores = [q.get("score", 0.5) for q in quiz_history[-10:]]
                retention_trend = np.polyfit(range(len(recent_scores)), recent_scores, 1)[0]
                avg_recent_score = np.mean(recent_scores)
                score_stability = 1.0 / (1.0 + np.std(recent_scores))

                features.extend([retention_trend, avg_recent_score, score_stability])
            else:
                features.extend([0, 0.5, 0.5])

            # Difficulty progression features
            difficulty_progression = user_data.get("difficulty_progression", {})
            current_level = difficulty_progression.get("current_level", 1) / 4.0  # Normalize
            progression_rate = difficulty_progression.get("progression_rate", 0.1)
            mastery_indicators = difficulty_progression.get("mastery_count", 0) / 100.0  # Normalize

            features.extend([current_level, progression_rate, mastery_indicators])

            # Temporal and engagement features
            now = datetime.now()
            day_of_week = now.weekday() / 6.0
            hour_of_day = now.hour / 23.0

            engagement = user_data.get("engagement", {})
            login_streak = min(engagement.get("login_streak", 0) / 30.0, 1.0)  # Normalize
            avg_session_duration = min(engagement.get("avg_session_duration", 0) / 3600.0, 1.0)  # Hours

            features.extend([day_of_week, hour_of_day, login_streak, avg_session_duration])

            # Medical-specific features
            clinical_case_performance = user_data.get("clinical_cases", {})
            case_accuracy = clinical_case_performance.get("accuracy", 0.5)
            case_completion = clinical_case_performance.get("completion_rate", 0.0)
            diagnostic_accuracy = clinical_case_performance.get("diagnostic_accuracy", 0.5)

            features.extend([case_accuracy, case_completion, diagnostic_accuracy])

            # Ensure fixed feature size (40 features total)
            target_size = 40
            while len(features) < target_size:
                features.append(0.0)
            features = features[:target_size]

            return np.array(features, dtype=np.float32)

        except Exception as e:
            logger.error(f"Error preparing medical features: {str(e)}")
            return np.zeros(40, dtype=np.float32)
    
    def train_medical_model(self, training_data: List[Dict[str, Any]],
                          performance_labels: List[float],
                          difficulty_labels: List[str]) -> Dict[str, Any]:
        """
        Train the medical education performance model with multi-output targets

        Args:
            training_data: List of user data dictionaries
            performance_labels: Binary labels for performance prediction (0/1)
            difficulty_labels: Categorical labels for difficulty level
        """
        try:
            # Prepare features
            X = np.array([self.prepare_medical_features(data) for data in training_data])
            y_performance = np.array(performance_labels)

            # Encode difficulty labels
            y_difficulty = tf.keras.utils.to_categorical(
                self.label_encoder.fit_transform(difficulty_labels),
                num_classes=len(self.difficulty_levels)
            )

            # Scale features
            X_scaled = self.scaler.fit_transform(X)

            # Split data
            X_train, X_test, y_perf_train, y_perf_test, y_diff_train, y_diff_test = train_test_split(
                X_scaled, y_performance, y_difficulty, test_size=0.2, random_state=42
            )

            # Create model
            self.model = self.create_model(X_train.shape[1])

            # Prepare training targets
            train_targets = {'performance': y_perf_train, 'difficulty': y_diff_train}
            test_targets = {'performance': y_perf_test, 'difficulty': y_diff_test}

            # Advanced callbacks for medical education
            callbacks = [
                tf.keras.callbacks.EarlyStopping(
                    monitor='val_performance_accuracy',
                    patience=15,
                    restore_best_weights=True
                ),
                tf.keras.callbacks.ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=10,
                    min_lr=1e-6
                ),
                tf.keras.callbacks.ModelCheckpoint(
                    filepath=os.path.join(self.model_path, 'best_model.h5'),
                    monitor='val_performance_accuracy',
                    save_best_only=True
                )
            ]

            # Train model
            history = self.model.fit(
                X_train, train_targets,
                epochs=100,
                batch_size=32,
                validation_data=(X_test, test_targets),
                verbose=1,
                callbacks=callbacks
            )

            # Evaluate model
            evaluation = self.model.evaluate(X_test, test_targets, verbose=0)

            self.is_trained = True

            # Save model and preprocessing objects
            self.save_model()

            # Calculate additional metrics
            predictions = self.model.predict(X_test, verbose=0)
            perf_predictions = (predictions[0] > 0.5).astype(int)
            diff_predictions = np.argmax(predictions[1], axis=1)

            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

            perf_accuracy = accuracy_score(y_perf_test, perf_predictions)
            perf_precision = precision_score(y_perf_test, perf_predictions, average='weighted')
            perf_recall = recall_score(y_perf_test, perf_predictions, average='weighted')
            perf_f1 = f1_score(y_perf_test, perf_predictions, average='weighted')

            diff_accuracy = accuracy_score(np.argmax(y_diff_test, axis=1), diff_predictions)

            return {
                "performance_accuracy": float(perf_accuracy),
                "performance_precision": float(perf_precision),
                "performance_recall": float(perf_recall),
                "performance_f1": float(perf_f1),
                "difficulty_accuracy": float(diff_accuracy),
                "epochs_trained": len(history.history['loss']),
                "final_loss": float(history.history['loss'][-1]),
                "final_val_loss": float(history.history['val_loss'][-1]),
                "model_architecture": "multi_output_medical_education"
            }

        except Exception as e:
            logger.error(f"Error training medical model: {str(e)}")
            raise
    
    def predict_medical_performance(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make comprehensive predictions for medical education performance

        Returns:
            - Performance prediction (success probability)
            - Recommended difficulty level
            - Subject-specific insights
            - Learning recommendations
        """
        try:
            if not self.is_trained and not self.load_model():
                return self._fallback_medical_prediction(user_data)

            # Prepare features
            features = self.prepare_medical_features(user_data)
            features_scaled = self.scaler.transform(features.reshape(1, -1))

            # Make predictions
            predictions = self.model.predict(features_scaled, verbose=0)
            performance_pred = predictions[0][0][0]  # Performance probability
            difficulty_pred = predictions[1][0]  # Difficulty level probabilities

            # Get recommended difficulty level
            recommended_difficulty_idx = np.argmax(difficulty_pred)
            recommended_difficulty = self.difficulty_levels[recommended_difficulty_idx]
            difficulty_confidence = float(difficulty_pred[recommended_difficulty_idx])

            # Calculate performance confidence
            performance_confidence = abs(performance_pred - 0.5) * 2

            # Generate subject-specific insights
            subject_insights = self._generate_subject_insights(user_data, features)

            # Generate learning recommendations
            recommendations = self._generate_learning_recommendations(
                performance_pred, recommended_difficulty, user_data
            )

            return {
                "predicted_success_rate": float(performance_pred),
                "performance_confidence": float(performance_confidence),
                "recommended_difficulty": recommended_difficulty,
                "difficulty_confidence": difficulty_confidence,
                "subject_insights": subject_insights,
                "learning_recommendations": recommendations,
                "model_used": "tensorflow_medical_education",
                "prediction_timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error making medical prediction: {str(e)}")
            return self._fallback_medical_prediction(user_data)
    
    def _generate_subject_insights(self, user_data: Dict[str, Any], features: np.ndarray) -> Dict[str, Any]:
        """Generate insights for each medical subject"""
        subject_performance = user_data.get("subject_performance", {})
        insights = {}

        for i, subject in enumerate(self.medical_subjects):
            # Extract subject-specific features (3 features per subject)
            start_idx = i * 3
            accuracy = features[start_idx] if start_idx < len(features) else 0.5
            completion = features[start_idx + 1] if start_idx + 1 < len(features) else 0.0
            time_spent = features[start_idx + 2] if start_idx + 2 < len(features) else 0.0

            # Generate insight based on performance
            if accuracy > 0.8 and completion > 0.7:
                status = "excellent"
                recommendation = f"Continue advanced {subject} topics"
            elif accuracy > 0.6 and completion > 0.5:
                status = "good"
                recommendation = f"Focus on challenging {subject} concepts"
            elif accuracy > 0.4:
                status = "needs_improvement"
                recommendation = f"Review fundamental {subject} principles"
            else:
                status = "requires_attention"
                recommendation = f"Intensive {subject} study recommended"

            insights[subject] = {
                "accuracy": float(accuracy),
                "completion_rate": float(completion),
                "time_invested": float(time_spent),
                "status": status,
                "recommendation": recommendation
            }

        return insights

    def _generate_learning_recommendations(self, performance_pred: float,
                                         difficulty: str, user_data: Dict[str, Any]) -> List[str]:
        """Generate personalized learning recommendations"""
        recommendations = []

        if performance_pred > 0.8:
            recommendations.extend([
                f"Excellent progress! Consider advancing to {difficulty} level content",
                "Focus on clinical case studies and practical applications",
                "Consider peer tutoring or teaching opportunities"
            ])
        elif performance_pred > 0.6:
            recommendations.extend([
                f"Good progress. Continue with {difficulty} level materials",
                "Increase practice with medical case scenarios",
                "Review challenging concepts regularly"
            ])
        elif performance_pred > 0.4:
            recommendations.extend([
                "Focus on fundamental concepts before advancing",
                "Increase study frequency and consistency",
                "Consider additional practice materials"
            ])
        else:
            recommendations.extend([
                "Intensive review of basic medical concepts recommended",
                "Consider seeking additional academic support",
                "Focus on one subject at a time for better retention"
            ])

        # Add subject-specific recommendations
        subject_performance = user_data.get("subject_performance", {})
        weak_subjects = [subj for subj, perf in subject_performance.items()
                        if perf.get("accuracy", 0.5) < 0.5]

        if weak_subjects:
            recommendations.append(f"Priority subjects for improvement: {', '.join(weak_subjects)}")

        return recommendations

    def _fallback_medical_prediction(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced fallback prediction for medical education"""
        subject_performance = user_data.get("subject_performance", {})

        if subject_performance:
            avg_accuracy = np.mean([perf.get("accuracy", 0.5)
                                  for perf in subject_performance.values()])
            avg_completion = np.mean([perf.get("completion_rate", 0.0)
                                    for perf in subject_performance.values()])
        else:
            avg_accuracy = 0.5
            avg_completion = 0.0

        # Heuristic-based prediction
        prediction = min(max((avg_accuracy * 0.7 + avg_completion * 0.3) * 1.1, 0.0), 1.0)

        # Simple difficulty recommendation
        if prediction > 0.8:
            difficulty = "advanced"
        elif prediction > 0.6:
            difficulty = "intermediate"
        else:
            difficulty = "beginner"

        return {
            "predicted_success_rate": prediction,
            "performance_confidence": 0.6,
            "recommended_difficulty": difficulty,
            "difficulty_confidence": 0.6,
            "subject_insights": {},
            "learning_recommendations": [
                "Model training required for detailed recommendations",
                "Continue regular study practice",
                "Focus on consistent learning habits"
            ],
            "model_used": "fallback_heuristic",
            "prediction_timestamp": datetime.now().isoformat()
        }
    
    def save_model(self):
        """Save the trained model and all preprocessing objects"""
        try:
            os.makedirs(self.model_path, exist_ok=True)

            # Save TensorFlow model
            self.model.save(os.path.join(self.model_path, "medical_model.h5"))

            # Save preprocessing objects
            with open(os.path.join(self.model_path, "scaler.pkl"), "wb") as f:
                pickle.dump(self.scaler, f)

            with open(os.path.join(self.model_path, "label_encoder.pkl"), "wb") as f:
                pickle.dump(self.label_encoder, f)

            # Save model metadata
            metadata = {
                "medical_subjects": self.medical_subjects,
                "difficulty_levels": self.difficulty_levels,
                "feature_size": 40,
                "model_version": "1.0",
                "created_at": datetime.now().isoformat()
            }

            with open(os.path.join(self.model_path, "metadata.json"), "w") as f:
                json.dump(metadata, f, indent=2)

            logger.info(f"Medical education model saved to {self.model_path}")

        except Exception as e:
            logger.error(f"Error saving medical model: {str(e)}")

    def load_model(self) -> bool:
        """Load a previously trained medical education model"""
        try:
            model_file = os.path.join(self.model_path, "medical_model.h5")
            scaler_file = os.path.join(self.model_path, "scaler.pkl")
            encoder_file = os.path.join(self.model_path, "label_encoder.pkl")
            metadata_file = os.path.join(self.model_path, "metadata.json")

            if all(os.path.exists(f) for f in [model_file, scaler_file, encoder_file]):
                # Load model
                self.model = tf.keras.models.load_model(model_file)

                # Load preprocessing objects
                with open(scaler_file, "rb") as f:
                    self.scaler = pickle.load(f)

                with open(encoder_file, "rb") as f:
                    self.label_encoder = pickle.load(f)

                # Load metadata if available
                if os.path.exists(metadata_file):
                    with open(metadata_file, "r") as f:
                        metadata = json.load(f)
                        logger.info(f"Loaded model version: {metadata.get('model_version', 'unknown')}")

                self.is_trained = True
                logger.info(f"Medical education model loaded from {self.model_path}")
                return True
            else:
                logger.warning(f"No complete medical model found at {self.model_path}")
                return False

        except Exception as e:
            logger.error(f"Error loading medical model: {str(e)}")
            return False

class LearningPatternAnalyzer:
    """
    TensorFlow-based learning pattern analysis
    """
    
    def __init__(self):
        self.autoencoder = None
        
    def create_autoencoder(self, input_dim: int) -> tf.keras.Model:
        """Create an autoencoder for pattern detection"""
        encoder = tf.keras.Sequential([
            tf.keras.layers.Dense(64, activation='relu', input_shape=(input_dim,)),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dense(16, activation='relu')
        ])
        
        decoder = tf.keras.Sequential([
            tf.keras.layers.Dense(32, activation='relu', input_shape=(16,)),
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dense(input_dim, activation='sigmoid')
        ])
        
        autoencoder = tf.keras.Sequential([encoder, decoder])
        autoencoder.compile(optimizer='adam', loss='mse')
        
        return autoencoder
    
    def analyze_patterns(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze learning patterns using TensorFlow"""
        try:
            # For now, return statistical analysis
            # TODO: Implement full TensorFlow-based pattern analysis
            study_times = user_data.get("study_time", [])
            
            if not study_times:
                return {"patterns": "insufficient_data"}
            
            # Extract time patterns
            durations = [s.get("total_duration", 0) for s in study_times]
            avg_duration = np.mean(durations)
            std_duration = np.std(durations)
            
            return {
                "average_study_duration": float(avg_duration),
                "study_consistency": float(1.0 / (1.0 + std_duration)),
                "pattern_type": "regular" if std_duration < avg_duration * 0.5 else "irregular",
                "model_used": "tensorflow_autoencoder"
            }
            
        except Exception as e:
            logger.error(f"Error analyzing patterns: {str(e)}")
            return {"patterns": "analysis_error", "error": str(e)}

# Global model instances
performance_model = MedicalEducationPerformanceModel()
pattern_analyzer = LearningPatternAnalyzer()
