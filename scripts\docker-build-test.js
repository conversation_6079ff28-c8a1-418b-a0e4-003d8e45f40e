#!/usr/bin/env node

/**
 * Docker Build Test Script for MedTrack Hub
 * Tests all Docker builds and reports issues
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkDockerAvailable() {
  try {
    const version = execSync('docker --version', { encoding: 'utf8' });
    colorLog(`✓ Docker is available: ${version.trim()}`, 'green');
    return true;
  } catch (error) {
    colorLog('✗ Docker is not available or not running', 'red');
    colorLog('Please start Docker Desktop and try again', 'yellow');
    return false;
  }
}

function checkDockerDaemon() {
  try {
    execSync('docker info', { encoding: 'utf8', stdio: 'ignore' });
    colorLog('✓ Docker daemon is running', 'green');
    return true;
  } catch (error) {
    colorLog('✗ Docker daemon is not running', 'red');
    colorLog('Please start Docker Desktop and try again', 'yellow');
    return false;
  }
}

async function buildDockerImage(serviceName, dockerfilePath, buildContext, imageTag) {
  colorLog(`\n🔨 Building ${serviceName}...`, 'blue');
  colorLog(`Context: ${buildContext}`, 'yellow');
  colorLog(`Dockerfile: ${dockerfilePath}`, 'yellow');
  
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const buildProcess = spawn('docker', [
      'build',
      '-t', imageTag,
      '-f', dockerfilePath,
      buildContext
    ], {
      stdio: ['inherit', 'pipe', 'pipe']
    });
    
    let stdout = '';
    let stderr = '';
    
    buildProcess.stdout.on('data', (data) => {
      stdout += data.toString();
      process.stdout.write(data);
    });
    
    buildProcess.stderr.on('data', (data) => {
      stderr += data.toString();
      process.stderr.write(data);
    });
    
    buildProcess.on('close', (code) => {
      const duration = ((Date.now() - startTime) / 1000).toFixed(1);
      
      if (code === 0) {
        colorLog(`✓ ${serviceName} build successful (${duration}s)`, 'green');
        
        // Check if image was created
        try {
          const imageExists = execSync(`docker images ${imageTag} --format "{{.Repository}}:{{.Tag}}"`, { encoding: 'utf8' }).trim();
          if (imageExists) {
            colorLog(`✓ Image ${imageTag} created successfully`, 'green');
            
            // Get image size
            try {
              const size = execSync(`docker images ${imageTag} --format "{{.Size}}"`, { encoding: 'utf8' }).trim();
              colorLog(`📦 Image size: ${size}`, 'cyan');
            } catch (e) {
              // Ignore size check errors
            }
            
            resolve({ success: true, duration, size: size || 'unknown' });
          } else {
            colorLog(`✗ Image ${imageTag} not found after build`, 'red');
            resolve({ success: false, error: 'Image not found after build' });
          }
        } catch (error) {
          colorLog(`✗ Error checking image: ${error.message}`, 'red');
          resolve({ success: false, error: error.message });
        }
      } else {
        colorLog(`✗ ${serviceName} build failed (exit code: ${code})`, 'red');
        resolve({ success: false, error: `Build failed with exit code ${code}`, stderr });
      }
    });
    
    buildProcess.on('error', (error) => {
      colorLog(`✗ Error building ${serviceName}: ${error.message}`, 'red');
      resolve({ success: false, error: error.message });
    });
  });
}

function testDockerCompose() {
  colorLog('\n🐳 Testing Docker Compose configuration...', 'blue');
  
  try {
    execSync('docker-compose config --quiet', { stdio: 'ignore' });
    colorLog('✓ Docker Compose configuration is valid', 'green');
    return true;
  } catch (error) {
    colorLog('✗ Docker Compose configuration has errors', 'red');
    try {
      const output = execSync('docker-compose config', { encoding: 'utf8' });
      colorLog('Docker Compose output:', 'yellow');
      console.log(output);
    } catch (e) {
      colorLog(`Error details: ${error.message}`, 'red');
    }
    return false;
  }
}

function checkRequiredFiles() {
  colorLog('\n📁 Checking required files...', 'blue');
  
  const requiredFiles = [
    'backend/Dockerfile',
    'backend/package.json',
    'frontend/Dockerfile',
    'frontend/package.json',
    'backend/python_analytics/Dockerfile',
    'backend/python_analytics/requirements.txt',
    'docker-compose.yml'
  ];
  
  let allFilesExist = true;
  
  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      colorLog(`✓ ${file}`, 'green');
    } else {
      colorLog(`✗ ${file} (missing)`, 'red');
      allFilesExist = false;
    }
  }
  
  return allFilesExist;
}

async function main() {
  colorLog('🚀 MedTrack Hub Docker Build Test', 'blue');
  colorLog('=================================', 'blue');
  
  // Check prerequisites
  if (!checkDockerAvailable() || !checkDockerDaemon()) {
    process.exit(1);
  }
  
  if (!checkRequiredFiles()) {
    colorLog('\n✗ Some required files are missing', 'red');
    process.exit(1);
  }
  
  // Define services to build
  const services = [
    {
      name: 'Backend API',
      dockerfilePath: 'backend/Dockerfile',
      buildContext: 'backend',
      imageTag: 'medtrack-backend:test'
    },
    {
      name: 'Frontend',
      dockerfilePath: 'frontend/Dockerfile',
      buildContext: 'frontend',
      imageTag: 'medtrack-frontend:test'
    },
    {
      name: 'Analytics Service',
      dockerfilePath: 'backend/python_analytics/Dockerfile',
      buildContext: 'backend/python_analytics',
      imageTag: 'medtrack-analytics:test'
    }
  ];
  
  const results = [];
  let allPassed = true;
  
  // Build each service
  for (const service of services) {
    const result = await buildDockerImage(
      service.name,
      service.dockerfilePath,
      service.buildContext,
      service.imageTag
    );
    
    results.push({ ...service, ...result });
    
    if (!result.success) {
      allPassed = false;
    }
  }
  
  // Test Docker Compose
  const composeSuccess = testDockerCompose();
  if (!composeSuccess) {
    allPassed = false;
  }
  
  // Summary
  colorLog('\n📊 Build Summary', 'blue');
  colorLog('================', 'blue');
  
  for (const result of results) {
    if (result.success) {
      colorLog(`✓ ${result.name}: ${result.duration}s (${result.size})`, 'green');
    } else {
      colorLog(`✗ ${result.name}: ${result.error}`, 'red');
    }
  }
  
  if (allPassed) {
    colorLog('\n🎉 All builds passed successfully!', 'green');
    colorLog('Ready for deployment!', 'green');
  } else {
    colorLog('\n✗ Some builds failed. Please check the errors above.', 'red');
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Usage: node docker-build-test.js [options]

Options:
  --help, -h    Show this help message

This script tests all Docker builds for the MedTrack Hub application.
Make sure Docker Desktop is running before executing this script.
  `);
  process.exit(0);
}

// Run the main function
main().catch((error) => {
  colorLog(`\n💥 Unexpected error: ${error.message}`, 'red');
  console.error(error);
  process.exit(1);
});
