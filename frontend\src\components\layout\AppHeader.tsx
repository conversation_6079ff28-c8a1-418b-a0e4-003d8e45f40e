'use client';

import React from 'react';
import {
  Menu,
  Search,
  Bell,
  User,
  Settings,
  LogOut,
  Sun,
  Moon,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  HelpCircle,
} from 'lucide-react';
import '@/app/components.css';
import { HeaderProps } from '@/types/navigation';
export const AppHeader: React.FC<HeaderProps> = ({
  sidebarOpen,
  setSidebarOpen,
  sidebarCollapsed,
  setSidebarCollapsed,
  searchQuery,
  setSearchQuery,
  theme,
  toggleTheme,
  notificationsOpen,
  setNotificationsOpen,
  userMenuOpen,
  setUserMenuOpen,
  user,
  notifications,
}) => {
  return (
    <header className="bg-card shadow-sm border-b border-border">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-2">
          {/* Mobile toggle button */}
          <button
            type="button"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors lg:hidden"
            aria-expanded={sidebarOpen ? 'true' : 'false'}
            aria-label="Toggle sidebar"
            title="Toggle Sidebar"
          >
            <Menu className="h-6 w-6" aria-hidden="true" />
          </button>

          {/* Desktop collapse button */}
          <button
            type="button"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="hidden lg:flex p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors"
            aria-expanded={!sidebarCollapsed ? 'true' : 'false'}
            aria-label="Collapse sidebar"
            title="Collapse Sidebar"
          >
            {sidebarCollapsed ? (
              <ChevronRight className="h-5 w-5" aria-hidden="true" />
            ) : (
              <ChevronLeft className="h-5 w-5" aria-hidden="true" />
            )}
          </button>
        </div>

        {/* Search */}
        <div className="relative hidden sm:block">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search courses, topics..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className={`search-input ${theme === 'dark' ? 'search-input-dark' : 'search-input-light'}`}
          />
        </div>

        {/* User Menu and Notifications */}
        <div className="flex items-center space-x-4">
          {/* Theme Toggle */}
          <button
            type="button"
            onClick={toggleTheme}
            className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            aria-label="Toggle theme"
            title="Toggle theme"
          >
            {theme === 'dark' ? (
              <Sun className="h-5 w-5 text-yellow-500 dark:text-yellow-400" />
            ) : (
              <Moon className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            )}
          </button>

          {/* Notifications */}
          <div className="relative">
            <button
              type="button"
              onClick={() => setNotificationsOpen(!notificationsOpen)}
              className="notifications-button relative p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Toggle notifications"
              aria-label="Toggle notifications"
              aria-expanded={notificationsOpen ? 'true' : 'false'}
            >
              <Bell className="h-5 w-5 text-gray-600 dark:text-gray-300" />
              <span
                className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"
                aria-live="polite"
                aria-label={`${notifications.filter(n => n.unread).length} unread notifications`}
              ></span>
            </button>

            {notificationsOpen && (
              <div
                className="notifications-menu absolute right-0 mt-2 w-80 bg-card border border-border rounded-radius-md shadow-lg z-50"
                role="region"
                aria-label="Notifications list"
              >
                <div className="p-4 border-b border-border">
                  <h3 className="font-semibold text-card-foreground">Notifications</h3>
                </div>
                <div className="max-h-80 overflow-y-auto">
                  {notifications.length === 0 ? (
                    <div className="p-4 text-center text-sm text-muted-foreground">
                      No notifications
                    </div>
                  ) : (
                    notifications.map(notification => (
                      <div
                        key={notification.id}
                        className="p-4 border-b border-border hover:bg-muted"
                      >
                        <div className="flex items-start space-x-3">
                          <div
                            className={`w-2 h-2 rounded-full mt-2 ${notification.unread ? 'bg-primary' : 'bg-muted'}`}
                          ></div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-card-foreground">
                              {notification.title}
                            </p>
                            <p className="text-sm text-muted-foreground mt-1">
                              {notification.message}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {notification.time}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>

          {/* User Menu */}
          <div className="relative">
            <button
              type="button"
              onClick={() => setUserMenuOpen(!userMenuOpen)}
              className="user-menu-button flex items-center space-x-2 p-2 rounded-radius-md hover:bg-muted transition-colors"
              aria-label="User menu"
              title="User menu"
              aria-expanded={userMenuOpen ? 'true' : 'false'}
            >
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-primary-foreground" />
              </div>
              <ChevronDown className="h-4 w-4 text-foreground" />
            </button>

            {userMenuOpen && (
              <div className="user-menu absolute right-0 mt-2 w-48 bg-card border border-border rounded-radius-md shadow-lg z-50">
                <div className="p-4 border-b border-border">
                  <p className="font-medium text-sm text-card-foreground">{user.name}</p>
                  <p className="text-xs text-muted-foreground">{user.email}</p>
                </div>
                <div className="p-2">
                  <button
                    type="button"
                    className="w-full flex items-center space-x-2 px-3 py-2 rounded-radius-sm hover:bg-muted text-left text-foreground"
                  >
                    <Settings className="h-4 w-4" />
                    <span className="text-sm">Settings</span>
                  </button>
                  <button
                    type="button"
                    className="w-full flex items-center space-x-2 px-3 py-2 rounded-radius-sm hover:bg-muted text-left text-foreground"
                  >
                    <HelpCircle className="h-4 w-4" />
                    <span className="text-sm">Help & Support</span>
                  </button>
                  <hr className="my-2 border-border" />
                  <button
                    type="button"
                    className="w-full flex items-center space-x-2 px-3 py-2 rounded-radius-sm hover:bg-muted text-left text-destructive"
                  >
                    <LogOut className="h-4 w-4" />
                    <span className="text-sm">Sign Out</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default AppHeader;
