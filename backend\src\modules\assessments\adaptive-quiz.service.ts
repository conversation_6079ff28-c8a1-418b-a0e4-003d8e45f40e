import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Question, QuestionDifficulty } from '../../entities/question.entity';
import { AssessmentAnswer } from '../../entities/assessment-answer.entity';
import { User } from '../../entities/user.entity';
import { QuestionBankService } from './question-bank.service';

export interface UserPerformanceProfile {
  userId: string;
  overallAbility: number; // Theta in IRT, ranges from -3 to +3
  categoryAbilities: Record<string, number>;
  recentPerformance: {
    correct: number;
    total: number;
    averageTime: number;
  };
  learningVelocity: number; // How quickly user is improving
  knowledgeGaps: string[];
  strengths: string[];
}

export interface AdaptiveQuizSession {
  sessionId: string;
  userId: string;
  questions: Question[];
  currentQuestionIndex: number;
  userAbility: number;
  confidenceInterval: number;
  targetPrecision: number;
  responses: {
    questionId: string;
    isCorrect: boolean;
    responseTime: number;
    confidence: number;
  }[];
  isComplete: boolean;
  finalScore: number;
  recommendations: string[];
}

@Injectable()
export class AdaptiveQuizService {
  private activeSessions = new Map<string, AdaptiveQuizSession>();

  constructor(
    @InjectRepository(Question)
    private readonly questionRepository: Repository<Question>,
    @InjectRepository(AssessmentAnswer)
    private readonly answerRepository: Repository<AssessmentAnswer>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly questionBankService: QuestionBankService,
  ) {}

  async startAdaptiveQuiz(
    userId: string,
    courseId?: string,
    unitId?: string,
  ): Promise<AdaptiveQuizSession> {
    // Get user's performance profile
    const userProfile = await this.getUserPerformanceProfile(userId);
    
    // Generate session ID
    const sessionId = `quiz_${userId}_${Date.now()}`;
    
    // Initialize session
    const session: AdaptiveQuizSession = {
      sessionId,
      userId,
      questions: [],
      currentQuestionIndex: 0,
      userAbility: userProfile.overallAbility,
      confidenceInterval: 2.0, // Start with wide confidence interval
      targetPrecision: 0.3, // Target standard error
      responses: [],
      isComplete: false,
      finalScore: 0,
      recommendations: [],
    };

    // Select initial question (medium difficulty)
    const initialQuestion = await this.selectNextQuestion(
      session,
      QuestionDifficulty.MEDIUM,
      courseId,
      unitId,
    );
    
    if (initialQuestion) {
      session.questions.push(initialQuestion);
    }

    // Store session
    this.activeSessions.set(sessionId, session);

    return session;
  }

  async submitAnswer(
    sessionId: string,
    questionId: string,
    answer: any,
    responseTime: number,
    confidence: number = 3,
  ): Promise<{
    isCorrect: boolean;
    explanation: string;
    nextQuestion?: Question;
    isComplete: boolean;
    session: AdaptiveQuizSession;
  }> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new NotFoundException('Quiz session not found');
    }

    const currentQuestion = session.questions[session.currentQuestionIndex];
    if (!currentQuestion || currentQuestion.id !== questionId) {
      throw new NotFoundException('Question not found in current session');
    }

    // Grade the answer
    const isCorrect = this.gradeAnswer(currentQuestion, answer);
    
    // Record response
    session.responses.push({
      questionId,
      isCorrect,
      responseTime,
      confidence,
    });

    // Update user ability using IRT
    session.userAbility = this.updateUserAbility(
      session.userAbility,
      currentQuestion.difficulty_index,
      isCorrect,
    );

    // Update confidence interval
    session.confidenceInterval = this.updateConfidenceInterval(
      session.confidenceInterval,
      currentQuestion.difficulty_index,
    );

    // Update question statistics
    await this.questionBankService.updateQuestionStatistics(
      questionId,
      isCorrect,
      responseTime,
    );

    // Move to next question
    session.currentQuestionIndex++;

    // Check if quiz should continue
    const shouldContinue = this.shouldContinueQuiz(session);
    
    let nextQuestion: Question | undefined;
    if (shouldContinue) {
      // Select next question based on current ability estimate
      const targetDifficulty = this.getTargetDifficulty(session.userAbility);
      nextQuestion = await this.selectNextQuestion(session, targetDifficulty);
      
      if (nextQuestion) {
        session.questions.push(nextQuestion);
      } else {
        // No more suitable questions available
        session.isComplete = true;
      }
    } else {
      session.isComplete = true;
    }

    // Finalize session if complete
    if (session.isComplete) {
      await this.finalizeSession(session);
    }

    return {
      isCorrect,
      explanation: currentQuestion.explanation || '',
      nextQuestion,
      isComplete: session.isComplete,
      session,
    };
  }

  private async getUserPerformanceProfile(userId: string): Promise<UserPerformanceProfile> {
    // Get recent answers for ability estimation
    const recentAnswers = await this.answerRepository.find({
      where: { attempt: { user_id: userId } },
      relations: ['question', 'attempt'],
      order: { created_at: 'DESC' },
      take: 50,
    });

    const correct = recentAnswers.filter(a => a.is_correct).length;
    const total = recentAnswers.length;
    
    // Simple ability estimation (can be enhanced with proper IRT)
    const overallAbility = total > 0 ? this.logit(correct / total) : 0;

    return {
      userId,
      overallAbility,
      categoryAbilities: {},
      recentPerformance: {
        correct,
        total,
        averageTime: 30, // Default
      },
      learningVelocity: 0.1,
      knowledgeGaps: [],
      strengths: [],
    };
  }

  private async selectNextQuestion(
    session: AdaptiveQuizSession,
    targetDifficulty: QuestionDifficulty,
    courseId?: string,
    unitId?: string,
  ): Promise<Question | null> {
    // Get questions not already used in this session
    const usedQuestionIds = session.questions.map(q => q.id);
    
    const queryBuilder = this.questionRepository
      .createQueryBuilder('question')
      .where('question.is_active = :is_active', { is_active: true })
      .andWhere('question.difficulty = :difficulty', { difficulty: targetDifficulty });

    if (usedQuestionIds.length > 0) {
      queryBuilder.andWhere('question.id NOT IN (:...usedIds)', { usedIds: usedQuestionIds });
    }

    if (courseId) {
      queryBuilder.andWhere('question.course_id = :courseId', { courseId });
    }

    if (unitId) {
      queryBuilder.andWhere('question.unit_id = :unitId', { unitId });
    }

    // Select question with optimal information value
    queryBuilder
      .orderBy('ABS(question.difficulty_index - :targetAbility)', 'ASC')
      .setParameter('targetAbility', this.abilityToDifficultyIndex(session.userAbility))
      .take(1);

    const questions = await queryBuilder.getMany();
    return questions.length > 0 ? questions[0] : null;
  }

  private gradeAnswer(question: Question, answer: any): boolean {
    // Simplified grading - can be enhanced based on question type
    if (question.correct_answer?.option_ids) {
      const correctIds = question.correct_answer.option_ids;
      if (Array.isArray(answer)) {
        // Multiple select
        return JSON.stringify(answer.sort()) === JSON.stringify(correctIds.sort());
      } else {
        // Single select
        return correctIds.includes(answer);
      }
    }
    return false;
  }

  private updateUserAbility(
    currentAbility: number,
    questionDifficulty: number,
    isCorrect: boolean,
  ): number {
    // Simplified IRT ability update
    const learningRate = 0.3;
    const expectedProbability = this.itemResponseFunction(currentAbility, questionDifficulty);
    const actualResponse = isCorrect ? 1 : 0;
    const error = actualResponse - expectedProbability;
    
    return currentAbility + learningRate * error;
  }

  private updateConfidenceInterval(
    currentInterval: number,
    questionDifficulty: number,
  ): number {
    // Fisher information approximation
    const information = questionDifficulty * (1 - questionDifficulty);
    return Math.sqrt(1 / (1 / (currentInterval * currentInterval) + information));
  }

  private shouldContinueQuiz(session: AdaptiveQuizSession): boolean {
    // Stop conditions
    if (session.responses.length >= 20) return false; // Max questions
    if (session.responses.length < 5) return true; // Min questions
    if (session.confidenceInterval <= session.targetPrecision) return false; // Target precision reached
    
    return true;
  }

  private getTargetDifficulty(ability: number): QuestionDifficulty {
    if (ability < -1) return QuestionDifficulty.EASY;
    if (ability > 1) return QuestionDifficulty.HARD;
    return QuestionDifficulty.MEDIUM;
  }

  private async finalizeSession(session: AdaptiveQuizSession): Promise<void> {
    // Calculate final score
    const correct = session.responses.filter(r => r.isCorrect).length;
    session.finalScore = (correct / session.responses.length) * 100;

    // Generate recommendations
    session.recommendations = this.generateRecommendations(session);

    // Clean up session after some time
    setTimeout(() => {
      this.activeSessions.delete(session.sessionId);
    }, 30 * 60 * 1000); // 30 minutes
  }

  private generateRecommendations(session: AdaptiveQuizSession): string[] {
    const recommendations: string[] = [];
    
    if (session.finalScore < 60) {
      recommendations.push('Review fundamental concepts before attempting more advanced topics');
    } else if (session.finalScore < 80) {
      recommendations.push('Focus on areas where you made mistakes');
    } else {
      recommendations.push('Great job! Consider advancing to more challenging material');
    }

    return recommendations;
  }

  // Utility functions
  private logit(p: number): number {
    p = Math.max(0.01, Math.min(0.99, p)); // Clamp to avoid infinity
    return Math.log(p / (1 - p));
  }

  private itemResponseFunction(ability: number, difficulty: number): number {
    return 1 / (1 + Math.exp(-(ability - difficulty)));
  }

  private abilityToDifficultyIndex(ability: number): number {
    // Convert ability (-3 to +3) to difficulty index (0 to 1)
    return (ability + 3) / 6;
  }
}
