'use client';

import React from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { LearningPathVisualization } from '@/components/learning-paths/LearningPathVisualization';

interface LearningPathDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function LearningPathDetailPage({ params }: LearningPathDetailPageProps) {
  const { id } = await params;

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <LearningPathVisualization pathId={id} />
      </div>
    </ProtectedRoute>
  );
}
