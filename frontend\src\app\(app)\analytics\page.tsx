'use client';

import React from 'react';
import { AnalyticsDashboard } from '@/components/AnalyticsDashboard';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

export default function AnalyticsPage() {
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Study Analytics</h1>
            <p className="text-gray-600">
              Track your progress, identify patterns, and get personalized recommendations to
              improve your study efficiency.
            </p>
          </div>
          <AnalyticsDashboard />
        </div>
      </div>
    </ProtectedRoute>
  );
}
