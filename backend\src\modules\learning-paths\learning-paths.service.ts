import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, In } from 'typeorm';
import { LearningPath, LearningPathType, LearningPathCategory, LearningPathStatus, LearningPathDifficulty } from '../../entities/learning-path.entity';
import { LearningPathProgress, LearningPathProgressStatus } from '../../entities/learning-path-progress.entity';
import { LearningPathMilestone } from '../../entities/learning-path-milestone.entity';
import { User } from '../../entities/user.entity';
import { Course } from '../../entities/course.entity';
import { Assessment } from '../../entities/assessment.entity';
import { ClinicalCase } from '../../entities/clinical-case.entity';

export interface CreateLearningPathDto {
  title: string;
  description: string;
  type?: LearningPathType;
  category: LearningPathCategory;
  difficulty: string;
  estimated_duration_weeks: number;
  estimated_hours_per_week: number;
  tags?: string[];
  learning_objectives?: string[];
  prerequisites?: any;
  path_structure: any;
  course_ids?: string[];
  assessment_ids?: string[];
  clinical_case_ids?: string[];
}

export interface UpdateLearningPathDto extends Partial<CreateLearningPathDto> {
  status?: LearningPathStatus;
}

export interface LearningPathFilters {
  category?: LearningPathCategory;
  difficulty?: string;
  status?: LearningPathStatus;
  type?: LearningPathType;
  tags?: string[];
  search?: string;
  created_by_id?: string;
}

@Injectable()
export class LearningPathsService {
  constructor(
    @InjectRepository(LearningPath)
    private learningPathRepository: Repository<LearningPath>,
    @InjectRepository(LearningPathProgress)
    private progressRepository: Repository<LearningPathProgress>,
    @InjectRepository(LearningPathMilestone)
    private milestoneRepository: Repository<LearningPathMilestone>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Course)
    private courseRepository: Repository<Course>,
    @InjectRepository(Assessment)
    private assessmentRepository: Repository<Assessment>,
    @InjectRepository(ClinicalCase)
    private clinicalCaseRepository: Repository<ClinicalCase>,
  ) {}

  async create(createDto: CreateLearningPathDto, userId: string): Promise<LearningPath> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Validate related resources
    const courses = createDto.course_ids ? 
      await this.courseRepository.find({ where: { id: In(createDto.course_ids) } }) : [];
    const assessments = createDto.assessment_ids ? 
      await this.assessmentRepository.find({ where: { id: In(createDto.assessment_ids) } }) : [];
    const clinicalCases = createDto.clinical_case_ids ? 
      await this.clinicalCaseRepository.find({ where: { id: In(createDto.clinical_case_ids) } }) : [];

    const learningPath = this.learningPathRepository.create({
      ...createDto,
      difficulty: createDto.difficulty as LearningPathDifficulty,
      created_by: user,
      courses,
      assessments,
      clinical_cases: clinicalCases,
      analytics: {
        total_enrollments: 0,
        completion_rate: 0,
        average_completion_time_weeks: 0,
        user_ratings: { average: 0, count: 0 },
        difficulty_feedback: { too_easy: 0, just_right: 0, too_hard: 0 },
      },
    });

    return await this.learningPathRepository.save(learningPath);
  }

  async findAll(filters: LearningPathFilters = {}): Promise<LearningPath[]> {
    const queryBuilder = this.learningPathRepository.createQueryBuilder('path')
      .leftJoinAndSelect('path.created_by', 'creator')
      .leftJoinAndSelect('path.courses', 'courses')
      .leftJoinAndSelect('path.assessments', 'assessments')
      .leftJoinAndSelect('path.clinical_cases', 'clinical_cases')
      .leftJoinAndSelect('path.milestones', 'milestones');

    if (filters.category) {
      queryBuilder.andWhere('path.category = :category', { category: filters.category });
    }

    if (filters.difficulty) {
      queryBuilder.andWhere('path.difficulty = :difficulty', { difficulty: filters.difficulty });
    }

    if (filters.status) {
      queryBuilder.andWhere('path.status = :status', { status: filters.status });
    }

    if (filters.type) {
      queryBuilder.andWhere('path.type = :type', { type: filters.type });
    }

    if (filters.created_by_id) {
      queryBuilder.andWhere('path.created_by_id = :created_by_id', { created_by_id: filters.created_by_id });
    }

    if (filters.tags && filters.tags.length > 0) {
      queryBuilder.andWhere('path.tags && :tags', { tags: filters.tags });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(path.title ILIKE :search OR path.description ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    queryBuilder.orderBy('path.created_at', 'DESC');

    return await queryBuilder.getMany();
  }

  async findOne(id: string): Promise<LearningPath> {
    const learningPath = await this.learningPathRepository.findOne({
      where: { id },
      relations: [
        'created_by',
        'template',
        'custom_instances',
        'courses',
        'assessments',
        'clinical_cases',
        'milestones',
        'user_progress',
      ],
    });

    if (!learningPath) {
      throw new NotFoundException('Learning path not found');
    }

    return learningPath;
  }

  async update(id: string, updateDto: UpdateLearningPathDto, userId: string): Promise<LearningPath> {
    const learningPath = await this.findOne(id);

    // Check if user has permission to update
    if (learningPath.created_by?.id !== userId) {
      throw new BadRequestException('You do not have permission to update this learning path');
    }

    // Update related resources if provided
    if (updateDto.course_ids) {
      learningPath.courses = await this.courseRepository.find({ 
        where: { id: In(updateDto.course_ids) } 
      });
    }

    if (updateDto.assessment_ids) {
      learningPath.assessments = await this.assessmentRepository.find({ 
        where: { id: In(updateDto.assessment_ids) } 
      });
    }

    if (updateDto.clinical_case_ids) {
      learningPath.clinical_cases = await this.clinicalCaseRepository.find({ 
        where: { id: In(updateDto.clinical_case_ids) } 
      });
    }

    Object.assign(learningPath, updateDto);
    return await this.learningPathRepository.save(learningPath);
  }

  async remove(id: string, userId: string): Promise<void> {
    const learningPath = await this.findOne(id);

    // Check if user has permission to delete
    if (learningPath.created_by?.id !== userId) {
      throw new BadRequestException('You do not have permission to delete this learning path');
    }

    await this.learningPathRepository.remove(learningPath);
  }

  async duplicate(id: string, userId: string, customizations?: Partial<CreateLearningPathDto>): Promise<LearningPath> {
    const originalPath = await this.findOne(id);
    
    const duplicateData: CreateLearningPathDto = {
      title: customizations?.title || `${originalPath.title} (Copy)`,
      description: customizations?.description || originalPath.description,
      type: LearningPathType.CUSTOM,
      category: customizations?.category || originalPath.category,
      difficulty: customizations?.difficulty || originalPath.difficulty,
      estimated_duration_weeks: customizations?.estimated_duration_weeks || originalPath.estimated_duration_weeks,
      estimated_hours_per_week: customizations?.estimated_hours_per_week || originalPath.estimated_hours_per_week,
      tags: customizations?.tags || originalPath.tags,
      learning_objectives: customizations?.learning_objectives || originalPath.learning_objectives,
      prerequisites: customizations?.prerequisites || originalPath.prerequisites,
      path_structure: customizations?.path_structure || originalPath.path_structure,
      course_ids: originalPath.courses?.map(c => c.id),
      assessment_ids: originalPath.assessments?.map(a => a.id),
      clinical_case_ids: originalPath.clinical_cases?.map(cc => cc.id),
    };

    const duplicatedPath = await this.create(duplicateData, userId);
    
    // Set template reference
    duplicatedPath.template = originalPath;
    return await this.learningPathRepository.save(duplicatedPath);
  }

  async getRecommendations(userId: string, limit: number = 5): Promise<LearningPath[]> {
    // Get user's learning history and preferences
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['learning_path_progress', 'course_enrollments'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Simple recommendation logic - can be enhanced with ML
    const queryBuilder = this.learningPathRepository.createQueryBuilder('path')
      .leftJoinAndSelect('path.created_by', 'creator')
      .leftJoinAndSelect('path.courses', 'courses')
      .leftJoinAndSelect('path.milestones', 'milestones')
      .where('path.status = :status', { status: LearningPathStatus.PUBLISHED })
      .andWhere('path.type = :type', { type: LearningPathType.TEMPLATE })
      .orderBy('path.analytics->>\'total_enrollments\'', 'DESC')
      .limit(limit);

    // Exclude paths user is already enrolled in
    const enrolledPathIds = user.learning_path_progress?.map(p => p.learning_path_id) || [];
    if (enrolledPathIds.length > 0) {
      queryBuilder.andWhere('path.id NOT IN (:...enrolledIds)', { enrolledIds: enrolledPathIds });
    }

    return await queryBuilder.getMany();
  }

  async getPopular(limit: number = 10): Promise<LearningPath[]> {
    return await this.learningPathRepository.find({
      where: { 
        status: LearningPathStatus.PUBLISHED,
        type: LearningPathType.TEMPLATE,
      },
      relations: ['created_by', 'courses', 'milestones'],
      order: { created_at: 'DESC' },
      take: limit,
    });
  }

  async getByCategory(category: LearningPathCategory): Promise<LearningPath[]> {
    return await this.learningPathRepository.find({
      where: { 
        category,
        status: LearningPathStatus.PUBLISHED,
      },
      relations: ['created_by', 'courses', 'milestones'],
      order: { created_at: 'DESC' },
    });
  }

  async updateAnalytics(id: string, analyticsUpdate: Partial<any>): Promise<void> {
    const learningPath = await this.findOne(id);
    
    learningPath.analytics = {
      ...learningPath.analytics,
      ...analyticsUpdate,
    };

    await this.learningPathRepository.save(learningPath);
  }
}
