/**
 * Authentication Module Tests
 *
 * This file contains tests for the authentication module.
 */

import { signIn, signOut } from 'next-auth/react';
import mockApiService from '../services/mockApiService';

// Mock next-auth
jest.mock('next-auth/react', () => ({
  signIn: jest.fn(),
  signOut: jest.fn(),
  useSession: jest.fn(() => ({
    data: {
      user: {
        id: 'test-user-id',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        accessToken: 'test-token',
      },
    },
    status: 'authenticated',
  })),
}));

describe('Authentication Module', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Login Functionality', () => {
    it('should call signIn with correct credentials', async () => {
      const credentials = {
        username: 'test',
        password: 'test',
        callbackUrl: '/dashboard',
      };

      await signIn('credentials', credentials);

      expect(signIn).toHaveBeenCalledWith('credentials', credentials);
    });

    it('should call signIn with remember me flag', async () => {
      const credentials = {
        username: 'test',
        password: 'test',
        callbackUrl: '/dashboard',
        rememberMe: true,
      };

      await signIn('credentials', credentials);

      expect(signIn).toHaveBeenCalledWith('credentials', credentials);
    });

    it('should handle login with mock API', async () => {
      const loginResponse = await mockApiService.login({
        email: '<EMAIL>',
        password: 'test',
      });

      expect(loginResponse).toBeDefined();
      expect(loginResponse.access_token).toBeDefined();
      expect(loginResponse.user).toBeDefined();
      expect(loginResponse.user.email).toBe('<EMAIL>');
      expect(loginResponse.token_expiration).toBe('1d'); // Default is 1 day
    });

    it('should handle login with remember me flag', async () => {
      const loginResponse = await mockApiService.login({
        email: '<EMAIL>',
        password: 'test',
        rememberMe: true,
      });

      expect(loginResponse).toBeDefined();
      expect(loginResponse.access_token).toBeDefined();
      expect(loginResponse.refresh_token).toBeDefined();
      expect(loginResponse.token_expiration).toBe('30d'); // 30 days with rememberMe
      expect(loginResponse.user).toBeDefined();
      expect(loginResponse.user.email).toBe('<EMAIL>');
    });
  });

  describe('Logout Functionality', () => {
    it('should call signOut when logging out', async () => {
      await signOut({ callbackUrl: '/auth/login' });

      expect(signOut).toHaveBeenCalledWith({ callbackUrl: '/auth/login' });
    });
  });

  describe('Profile Functionality', () => {
    it('should retrieve user profile from mock API', async () => {
      const profile = await mockApiService.getProfile();

      expect(profile).toBeDefined();
      expect(profile.id).toBeDefined();
      expect(profile.email).toBeDefined();
      expect(profile.role).toBeDefined();
    });
  });

  describe('Password Reset Functionality', () => {
    it('should handle forgot password request', async () => {
      const response = await mockApiService.forgotPassword('<EMAIL>');

      expect(response).toBeDefined();
      expect(response.message).toBe('Password reset email sent');
      // In development mode, we also get the token
      expect(response.resetToken).toBeDefined();
    });

    it('should reject forgot password for non-existent user', async () => {
      await expect(mockApiService.forgotPassword('<EMAIL>')).rejects.toThrow(
        'User not found'
      );
    });

    it('should handle password reset with valid token', async () => {
      const response = await mockApiService.resetPassword('mock-reset-token', 'newPassword123');

      expect(response).toBeDefined();
      expect(response.message).toBe('Password reset successful');
    });

    it('should reject password reset with invalid token', async () => {
      await expect(mockApiService.resetPassword('invalid-token', 'newPassword123')).rejects.toThrow(
        'Invalid or expired reset token'
      );
    });
  });
});
