import { DataSource } from 'typeorm';

export async function seedLearningPathsSimple(dataSource: DataSource) {
  console.log('Starting simple learning paths seeding...');
  
  try {
    // Create a system user if it doesn't exist
    await dataSource.query(`
      INSERT INTO users (id, email, username, name, first_name, last_name, password_hash, role, email_verified, "createdAt", "updatedAt")
      VALUES (
        gen_random_uuid(),
        '<EMAIL>',
        'system_admin',
        'System Administrator',
        'System',
        'Administrator',
        '$2b$10$example.hash.here',
        'admin',
        true,
        NOW(),
        NOW()
      )
      ON CONFLICT (email) DO NOTHING;
    `);

    // Get the system user ID
    const systemUserResult = await dataSource.query(`
      SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1;
    `);
    
    if (systemUserResult.length === 0) {
      throw new Error('Failed to create or find system user');
    }
    
    const systemUserId = systemUserResult[0].id;

    // Create USMLE Step 1 Learning Path
    await dataSource.query(`
      INSERT INTO learning_paths (
        id, title, description, type, category, difficulty, status,
        estimated_duration_weeks, estimated_hours_per_week, tags, learning_objectives,
        prerequisites, path_structure, analytics, created_by_id, created_at, updated_at
      ) VALUES (
        gen_random_uuid(),
        'USMLE Step 1 Mastery Path',
        'Comprehensive preparation for USMLE Step 1 exam covering all major medical disciplines',
        'template',
        'usmle_step1',
        'intermediate',
        'published',
        16,
        25,
        ARRAY['usmle', 'step1', 'medical-boards'],
        ARRAY['Master fundamental medical sciences', 'Develop clinical reasoning skills'],
        '{"courses": ["basic-sciences-foundation"], "description": "Pre-clinical completion required"}',
        '{"phases": [{"id": "phase_1", "title": "Foundation Review", "description": "Review fundamentals", "order": 1, "estimated_duration_weeks": 4, "modules": [{"id": "module_1", "title": "Anatomy Review", "description": "Basic anatomy", "type": "course", "estimated_duration_hours": 40, "is_required": true, "order": 1}]}]}',
        '{"total_enrollments": 0, "completion_rate": 0, "average_completion_time_weeks": 0, "user_ratings": {"average": 0, "count": 0}, "difficulty_feedback": {"too_easy": 0, "just_right": 0, "too_hard": 0}}',
        $1,
        NOW(),
        NOW()
      )
      ON CONFLICT DO NOTHING;
    `, [systemUserId]);

    // Create Clinical Skills Learning Path
    await dataSource.query(`
      INSERT INTO learning_paths (
        id, title, description, type, category, difficulty, status,
        estimated_duration_weeks, estimated_hours_per_week, tags, learning_objectives,
        prerequisites, path_structure, analytics, created_by_id, created_at, updated_at
      ) VALUES (
        gen_random_uuid(),
        'Clinical Skills Mastery',
        'Develop essential clinical skills including physical examination and patient communication',
        'template',
        'clinical_skills',
        'intermediate',
        'published',
        12,
        15,
        ARRAY['clinical-skills', 'physical-exam', 'patient-care'],
        ARRAY['Master physical examination techniques', 'Develop patient communication skills'],
        '{"courses": ["basic-clinical-introduction"], "description": "Basic clinical introduction required"}',
        '{"phases": [{"id": "phase_1", "title": "Physical Exam Fundamentals", "description": "Basic physical exam", "order": 1, "estimated_duration_weeks": 4, "modules": [{"id": "module_1", "title": "General Physical Exam", "description": "Systematic approach", "type": "course", "estimated_duration_hours": 20, "is_required": true, "order": 1}]}]}',
        '{"total_enrollments": 0, "completion_rate": 0, "average_completion_time_weeks": 0, "user_ratings": {"average": 0, "count": 0}, "difficulty_feedback": {"too_easy": 0, "just_right": 0, "too_hard": 0}}',
        $1,
        NOW(),
        NOW()
      )
      ON CONFLICT DO NOTHING;
    `, [systemUserId]);

    // Create Internal Medicine Learning Path
    await dataSource.query(`
      INSERT INTO learning_paths (
        id, title, description, type, category, difficulty, status,
        estimated_duration_weeks, estimated_hours_per_week, tags, learning_objectives,
        prerequisites, path_structure, analytics, created_by_id, created_at, updated_at
      ) VALUES (
        gen_random_uuid(),
        'Internal Medicine Residency Prep',
        'Comprehensive preparation for internal medicine residency applications and interviews',
        'template',
        'residency_prep',
        'advanced',
        'published',
        20,
        30,
        ARRAY['internal-medicine', 'residency', 'clinical-knowledge'],
        ARRAY['Master internal medicine knowledge', 'Develop advanced clinical reasoning'],
        '{"courses": ["clinical-rotations-core"], "description": "Core clinical rotations completed"}',
        '{"phases": [{"id": "phase_1", "title": "Core IM Knowledge", "description": "Essential internal medicine", "order": 1, "estimated_duration_weeks": 8, "modules": [{"id": "module_1", "title": "Cardiology Essentials", "description": "Core cardiology", "type": "course", "estimated_duration_hours": 40, "is_required": true, "order": 1}]}]}',
        '{"total_enrollments": 0, "completion_rate": 0, "average_completion_time_weeks": 0, "user_ratings": {"average": 0, "count": 0}, "difficulty_feedback": {"too_easy": 0, "just_right": 0, "too_hard": 0}}',
        $1,
        NOW(),
        NOW()
      )
      ON CONFLICT DO NOTHING;
    `, [systemUserId]);

    console.log('✅ Learning paths seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding learning paths:', error);
    throw error;
  }
}
