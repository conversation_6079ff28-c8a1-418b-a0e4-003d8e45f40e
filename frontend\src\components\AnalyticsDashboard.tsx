'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/Button';
import {
  TrendingUp,
  TrendingDown,
  Target,
  Clock,
  Brain,
  Award,
  AlertCircle,
  RefreshCw,
  BarChart3,
  PieChart,
  Activity,
  BookOpen,
  Zap,
  Calendar,
  Users,
} from 'lucide-react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart,
  Pie,
  Cell,
  BarChart,
  Bar,
  AreaChart,
  Area,
} from 'recharts';

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

// Mock data for demo purposes
const mockAnalyticsData = {
  performanceMetrics: {
    totalCards: 245,
    cardsDue: 23,
    averageInterval: 12.5,
    successRate: 0.78,
    studyStreak: 7,
    totalStudyTime: 1240, // minutes
    averageSessionDuration: 35,
    weeklyGoalProgress: 0.65,
  },
  studyPatterns: {
    preferredStudyTimes: {
      morning: 0.25,
      afternoon: 0.45,
      evening: 0.3,
    },
    performanceByTopic: {
      Cardiology: 0.85,
      Neurology: 0.72,
      Pharmacology: 0.68,
      Anatomy: 0.91,
      Physiology: 0.76,
      Pathology: 0.63,
    },
    weeklyProgress: [
      { day: 'Mon', studyTime: 45, score: 78 },
      { day: 'Tue', studyTime: 60, score: 82 },
      { day: 'Wed', studyTime: 30, score: 75 },
      { day: 'Thu', studyTime: 55, score: 88 },
      { day: 'Fri', studyTime: 40, score: 79 },
      { day: 'Sat', studyTime: 70, score: 85 },
      { day: 'Sun', studyTime: 35, score: 73 },
    ],
  },
  predictions: {
    predictedSuccessRate: 0.82,
    confidence: 0.89,
    recommendedStudyTime: 45,
    improvementAreas: [
      { topic: 'Pathology', currentScore: 63, potentialScore: 78 },
      { topic: 'Pharmacology', currentScore: 68, potentialScore: 81 },
      { topic: 'Neurology', currentScore: 72, potentialScore: 84 },
    ],
  },
  recommendations: {
    focusAreas: ['Pathology', 'Pharmacology'],
    studySchedule: [
      { day: 'Today', topics: ['Pathology Review', 'Cardiology Practice'], duration: 45 },
      { day: 'Tomorrow', topics: ['Pharmacology Quiz', 'Anatomy Flashcards'], duration: 50 },
      { day: 'Wednesday', topics: ['Neurology Cases', 'Physiology Review'], duration: 40 },
    ],
  },
};

export function AnalyticsDashboard() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState(mockAnalyticsData);
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceBadge = (score: number) => {
    if (score >= 0.8) return 'bg-green-100 text-green-800';
    if (score >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  if (error) {
    return (
      <div className="flex items-center justify-center p-8 text-red-600">
        <AlertCircle className="mr-2 h-5 w-5" />
        <span>{error}</span>
      </div>
    );
  }

  if (isLoading) {
    return <AnalyticsSkeleton />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
          <p className="text-gray-600">Track your learning progress and performance insights</p>
        </div>
        <Button onClick={handleRefresh} variant="outline" size="sm" disabled={refreshing}>
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="patterns">Patterns</TabsTrigger>
          <TabsTrigger value="predictions">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Study Streak</CardTitle>
                <Award className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.performanceMetrics.studyStreak}</div>
                <p className="text-xs text-gray-600">days in a row</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                <Target className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.round(data.performanceMetrics.successRate * 100)}%
                </div>
                <Progress value={data.performanceMetrics.successRate * 100} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Cards Due</CardTitle>
                <Brain className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.performanceMetrics.cardsDue}</div>
                <p className="text-xs text-gray-600">
                  of {data.performanceMetrics.totalCards} total
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Study Time</CardTitle>
                <Clock className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatTime(data.performanceMetrics.totalStudyTime)}
                </div>
                <p className="text-xs text-gray-600">this week</p>
              </CardContent>
            </Card>
          </div>

          {/* Weekly Progress Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Weekly Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={data.studyPatterns.weeklyProgress}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Area
                    yAxisId="left"
                    type="monotone"
                    dataKey="studyTime"
                    stackId="1"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.3}
                    name="Study Time (min)"
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="score"
                    stroke="#10b981"
                    strokeWidth={2}
                    name="Score (%)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          {/* Topic Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Performance by Topic</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(data.studyPatterns.performanceByTopic).map(([topic, score]) => (
                  <div key={topic} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{topic}</span>
                      <div className="flex items-center gap-2">
                        <Badge className={getPerformanceBadge(score)}>
                          {Math.round(score * 100)}%
                        </Badge>
                        {score >= 0.8 ? (
                          <TrendingUp className="h-4 w-4 text-green-600" />
                        ) : score >= 0.6 ? (
                          <Activity className="h-4 w-4 text-yellow-600" />
                        ) : (
                          <TrendingDown className="h-4 w-4 text-red-600" />
                        )}
                      </div>
                    </div>
                    <Progress value={score * 100} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Study Time Distribution */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Study Time Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <RechartsPieChart>
                    <Pie
                      data={[
                        {
                          name: 'Morning',
                          value: data.studyPatterns.preferredStudyTimes.morning * 100,
                        },
                        {
                          name: 'Afternoon',
                          value: data.studyPatterns.preferredStudyTimes.afternoon * 100,
                        },
                        {
                          name: 'Evening',
                          value: data.studyPatterns.preferredStudyTimes.evening * 100,
                        },
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${percent.toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {[0, 1, 2].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Weekly Goal Progress</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">
                    {Math.round(data.performanceMetrics.weeklyGoalProgress * 100)}%
                  </div>
                  <p className="text-sm text-gray-600">of weekly goal completed</p>
                </div>
                <Progress
                  value={data.performanceMetrics.weeklyGoalProgress * 100}
                  className="h-3"
                />
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Target</p>
                    <p className="font-semibold">20 hours/week</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Completed</p>
                    <p className="font-semibold">
                      {formatTime(data.performanceMetrics.totalStudyTime)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="patterns" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Study Session Patterns</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Average Session</span>
                    <span className="font-semibold">
                      {data.performanceMetrics.averageSessionDuration} min
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Total Sessions</span>
                    <span className="font-semibold">35 this week</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Consistency Score</span>
                    <Badge className="bg-green-100 text-green-800">85%</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Learning Velocity</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={data.studyPatterns.weeklyProgress}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="day" />
                    <YAxis />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="studyTime"
                      stroke="#3b82f6"
                      strokeWidth={2}
                      name="Study Time (min)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="predictions" className="space-y-6">
          {/* Performance Predictions */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Performance Prediction</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-4xl font-bold text-blue-600">
                    {Math.round(data.predictions.predictedSuccessRate * 100)}%
                  </div>
                  <p className="text-sm text-gray-600">Predicted success rate</p>
                </div>
                <Progress value={data.predictions.predictedSuccessRate * 100} className="h-3" />
                <div className="flex items-center justify-between text-sm">
                  <span>Confidence Level</span>
                  <Badge className="bg-blue-100 text-blue-800">
                    {Math.round(data.predictions.confidence * 100)}%
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recommended Study Time</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-4xl font-bold text-green-600">
                    {data.predictions.recommendedStudyTime}
                  </div>
                  <p className="text-sm text-gray-600">minutes per day</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Current Average</span>
                    <span>{data.performanceMetrics.averageSessionDuration} min</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Improvement</span>
                    <span className="text-green-600">
                      +
                      {data.predictions.recommendedStudyTime -
                        data.performanceMetrics.averageSessionDuration}{' '}
                      min
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Improvement Areas */}
          <Card>
            <CardHeader>
              <CardTitle>Improvement Opportunities</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.predictions.improvementAreas.map(area => (
                  <div key={area.topic} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">{area.topic}</h4>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">
                          {area.currentScore}% → {area.potentialScore}%
                        </span>
                        <TrendingUp className="h-4 w-4 text-green-600" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs text-gray-600">
                        <span>Current</span>
                        <span>Potential</span>
                      </div>
                      <div className="relative">
                        <Progress value={area.currentScore} className="h-2" />
                        <div
                          className="absolute top-0 h-2 bg-green-200 rounded-full"
                          style={{
                            left: `${area.currentScore}%`,
                            width: `${area.potentialScore - area.currentScore}%`,
                          }}
                        />
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">
                      Potential improvement: +{area.potentialScore - area.currentScore} points
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Study Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle>Personalized Study Plan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.recommendations.studySchedule.map(day => (
                  <div
                    key={day.day}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-blue-600" />
                      <div>
                        <h4 className="font-semibold">{day.day}</h4>
                        <p className="text-sm text-gray-600">{day.topics.join(', ')}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{day.duration} min</div>
                      <div className="text-xs text-gray-600">recommended</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

function AnalyticsSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <div className="h-8 w-64 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 w-96 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="h-9 w-20 bg-gray-200 rounded animate-pulse" />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[1, 2, 3, 4].map(i => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 w-4 bg-gray-200 rounded animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-gray-200 rounded animate-pulse mb-2" />
              <div className="h-2 w-full bg-gray-200 rounded animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <div className="h-6 w-32 bg-gray-200 rounded animate-pulse" />
        </CardHeader>
        <CardContent>
          <div className="h-64 w-full bg-gray-200 rounded animate-pulse" />
        </CardContent>
      </Card>
    </div>
  );
}
