'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';

const years = ['1st Year', '2nd Year', '3rd Year', '4th Year', '5th Year', 'Graduate'];
const specialties = [
  'General Medicine',
  'Surgery',
  'Pediatrics',
  'Obstetrics & Gynecology',
  'Psychiatry',
  'Other',
];
const preferences = ['Visual', 'Reading/Writing', 'Practice/Hands-on', 'Group Study', 'Solo Study'];

const tourSlides = [
  {
    title: 'Your Dashboard',
    description:
      'Track your progress, set goals, and access all your learning tools from one place.',
  },
  {
    title: 'Rapid Review',
    description:
      'Quickly reinforce weak areas with high-yield, fast-paced quizzes tailored to you.',
  },
  {
    title: 'Study Groups',
    description: 'Join or create groups to collaborate, compete, and learn together with peers.',
  },
];

export default function OnboardingPage() {
  const [step, setStep] = useState(0);
  const [profile, setProfile] = useState({
    name: '',
    year: '',
    specialty: '',
    preference: '',
  });
  const [goals, setGoals] = useState({
    daily: '',
    weekly: '',
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [tourIndex, setTourIndex] = useState(0);
  const [finished, setFinished] = useState(false);

  // Step 0: Welcome
  if (step === 0) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-8">
        <div className="w-full max-w-lg bg-white rounded-xl shadow p-8 flex flex-col items-center">
          <h1 className="text-3xl font-bold mb-4 text-blue-700">Welcome to MedTrack Hub!</h1>
          <p className="text-gray-600 mb-6 text-center">
            We’re excited to help you master medicine. Let’s set up your profile and goals so you
            can get the most out of your learning journey.
          </p>
          <Button
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold shadow"
            onClick={() => setStep(1)}
          >
            Get Started
          </Button>
        </div>
      </div>
    );
  }

  // Step 1: Profile Setup
  if (step === 1) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-8">
        <div className="w-full max-w-lg bg-white rounded-xl shadow p-8 flex flex-col items-center">
          <h2 className="text-2xl font-bold mb-4 text-blue-700">Set Up Your Profile</h2>
          <form
            className="w-full space-y-4"
            onSubmit={async e => {
              e.preventDefault();
              setError('');
              if (!profile.name || !profile.year || !profile.specialty || !profile.preference) {
                setError('Please fill in all fields.');
                return;
              }
              setLoading(true);
              // Simulate saving profile to backend
              await new Promise(res => setTimeout(res, 1000));
              setLoading(false);
              setStep(2);
            }}
          >
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
              <input
                type="text"
                className="w-full rounded border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                value={profile.name}
                onChange={e => setProfile(p => ({ ...p, name: e.target.value }))}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Year of Study</label>
              <select
                className="w-full rounded border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                value={profile.year}
                onChange={e => setProfile(p => ({ ...p, year: e.target.value }))}
                required
              >
                <option value="">Select year</option>
                {years.map(y => (
                  <option key={y} value={y}>
                    {y}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Specialty Interest
              </label>
              <select
                className="w-full rounded border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                value={profile.specialty}
                onChange={e => setProfile(p => ({ ...p, specialty: e.target.value }))}
                required
              >
                <option value="">Select specialty</option>
                {specialties.map(s => (
                  <option key={s} value={s}>
                    {s}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Study Preference
              </label>
              <select
                className="w-full rounded border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                value={profile.preference}
                onChange={e => setProfile(p => ({ ...p, preference: e.target.value }))}
                required
              >
                <option value="">Select preference</option>
                {preferences.map(p => (
                  <option key={p} value={p}>
                    {p}
                  </option>
                ))}
              </select>
            </div>
            {error && <div className="text-red-500 text-sm">{error}</div>}
            <Button
              type="submit"
              size="lg"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold shadow mt-2"
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Continue'}
            </Button>
          </form>
          <Button variant="outline" className="mt-4" onClick={() => setStep(0)} disabled={loading}>
            Back to Welcome
          </Button>
        </div>
      </div>
    );
  }

  // Step 2: Learning Goals
  if (step === 2) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-8">
        <div className="w-full max-w-lg bg-white rounded-xl shadow p-8 flex flex-col items-center">
          <h2 className="text-2xl font-bold mb-4 text-blue-700">Set Your Learning Goals</h2>
          <form
            className="w-full space-y-4"
            onSubmit={async e => {
              e.preventDefault();
              setError('');
              if (!goals.daily || !goals.weekly) {
                setError('Please set both daily and weekly targets.');
                return;
              }
              setLoading(true);
              // Simulate saving goals to backend
              await new Promise(res => setTimeout(res, 1000));
              setLoading(false);
              setStep(3);
            }}
          >
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Daily Study Target (minutes)
              </label>
              <input
                type="number"
                min="5"
                max="300"
                className="w-full rounded border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                value={goals.daily}
                onChange={e => setGoals(g => ({ ...g, daily: e.target.value }))}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Weekly Study Target (minutes)
              </label>
              <input
                type="number"
                min="30"
                max="2000"
                className="w-full rounded border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                value={goals.weekly}
                onChange={e => setGoals(g => ({ ...g, weekly: e.target.value }))}
                required
              />
            </div>
            {error && <div className="text-red-500 text-sm">{error}</div>}
            <Button
              type="submit"
              size="lg"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold shadow mt-2"
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Continue'}
            </Button>
          </form>
          <Button variant="outline" className="mt-4" onClick={() => setStep(1)} disabled={loading}>
            Back to Profile Setup
          </Button>
        </div>
      </div>
    );
  }

  // Step 3: Quick Tour
  if (step === 3) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-8">
        <div className="w-full max-w-lg bg-white rounded-xl shadow p-8 flex flex-col items-center">
          <h2 className="text-2xl font-bold mb-4 text-blue-700">Quick Platform Tour</h2>
          <div className="w-full flex flex-col items-center mb-6">
            <div className="text-xl font-semibold mb-2">{tourSlides[tourIndex].title}</div>
            <div className="text-gray-600 text-center mb-4">
              {tourSlides[tourIndex].description}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                disabled={tourIndex === 0}
                onClick={() => setTourIndex(i => Math.max(0, i - 1))}
              >
                Previous
              </Button>
              {tourIndex < tourSlides.length - 1 ? (
                <Button onClick={() => setTourIndex(i => i + 1)}>Next</Button>
              ) : (
                <Button
                  className="bg-green-600 hover:bg-green-700 text-white"
                  onClick={async () => {
                    setLoading(true);
                    // Simulate saving all onboarding data to backend
                    await new Promise(res => setTimeout(res, 1000));
                    setLoading(false);
                    setFinished(true);
                  }}
                >
                  Finish
                </Button>
              )}
            </div>
          </div>
          <Button variant="outline" className="mt-2" onClick={() => setStep(2)} disabled={loading}>
            Back to Learning Goals
          </Button>
        </div>
      </div>
    );
  }

  // Step 4: Confirmation
  if (finished) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-8">
        <div className="w-full max-w-lg bg-white rounded-xl shadow p-8 flex flex-col items-center">
          <h2 className="text-2xl font-bold mb-4 text-green-700">You're All Set!</h2>
          <p className="text-gray-600 mb-6 text-center">
            Your profile and goals are saved. Explore your dashboard and start your learning
            journey!
          </p>
          <Button
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold shadow"
            onClick={() => (window.location.href = '/dashboard')}
          >
            Go to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  // Placeholder for any unexpected state
  return null;
}
