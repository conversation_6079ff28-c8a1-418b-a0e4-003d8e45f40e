import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AssessmentAnswer } from '../../entities/assessment-answer.entity';
import { Question, QuestionCategory } from '../../entities/question.entity';
import { User } from '../../entities/user.entity';
import { Course } from '../../entities/course.entity';

export interface DetailedFeedback {
  questionId: string;
  isCorrect: boolean;
  userAnswer: any;
  correctAnswer: any;
  explanation: string;
  conceptsToReview: string[];
  relatedResources: {
    type: 'article' | 'video' | 'practice';
    title: string;
    url: string;
    description: string;
  }[];
  difficultyAnalysis: {
    questionDifficulty: string;
    userPerformanceLevel: string;
    recommendation: string;
  };
  timeAnalysis: {
    timeSpent: number;
    averageTime: number;
    efficiency: 'fast' | 'optimal' | 'slow';
    suggestion: string;
  };
}

export interface PerformanceAnalytics {
  userId: string;
  assessmentId?: string;
  overallScore: number;
  categoryBreakdown: {
    category: QuestionCategory;
    score: number;
    questionsAnswered: number;
    averageTime: number;
    strengths: string[];
    weaknesses: string[];
  }[];
  learningTrends: {
    date: Date;
    score: number;
    category: QuestionCategory;
  }[];
  knowledgeGaps: {
    topic: string;
    severity: 'low' | 'medium' | 'high';
    recommendedActions: string[];
  }[];
  studyRecommendations: {
    priority: 'high' | 'medium' | 'low';
    topic: string;
    estimatedStudyTime: number;
    resources: string[];
  }[];
  nextSteps: string[];
}

@Injectable()
export class FeedbackService {
  constructor(
    @InjectRepository(AssessmentAnswer)
    private readonly answerRepository: Repository<AssessmentAnswer>,
    @InjectRepository(Question)
    private readonly questionRepository: Repository<Question>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
  ) {}

  async generateDetailedFeedback(
    userId: string,
    questionId: string,
    userAnswer: any,
    responseTime: number,
  ): Promise<DetailedFeedback> {
    const question = await this.questionRepository.findOne({
      where: { id: questionId },
      relations: ['course', 'unit'],
    });

    if (!question) {
      throw new Error('Question not found');
    }

    const isCorrect = this.evaluateAnswer(question, userAnswer);
    const explanation = this.generateExplanation(question, userAnswer, isCorrect);
    const conceptsToReview = this.identifyConceptsToReview(question, isCorrect);
    const relatedResources = await this.getRelatedResources(question, isCorrect);
    const difficultyAnalysis = await this.analyzeDifficulty(userId, question);
    const timeAnalysis = this.analyzeResponseTime(responseTime, question);

    return {
      questionId,
      isCorrect,
      userAnswer,
      correctAnswer: question.correct_answer,
      explanation,
      conceptsToReview,
      relatedResources,
      difficultyAnalysis,
      timeAnalysis,
    };
  }

  async generatePerformanceAnalytics(
    userId: string,
    assessmentId?: string,
    timeframe?: { start: Date; end: Date },
  ): Promise<PerformanceAnalytics> {
    // Get user's answers within timeframe
    let queryBuilder = this.answerRepository
      .createQueryBuilder('answer')
      .leftJoinAndSelect('answer.question', 'question')
      .where('answer.user_id = :userId', { userId });

    if (assessmentId) {
      queryBuilder.andWhere('answer.assessment_id = :assessmentId', { assessmentId });
    }

    if (timeframe) {
      queryBuilder.andWhere('answer.created_at BETWEEN :start AND :end', {
        start: timeframe.start,
        end: timeframe.end,
      });
    }

    const answers = await queryBuilder.getMany();

    // Calculate overall score
    const correctAnswers = answers.filter(a => a.is_correct).length;
    const overallScore = answers.length > 0 ? (correctAnswers / answers.length) * 100 : 0;

    // Generate category breakdown
    const categoryBreakdown = this.generateCategoryBreakdown(answers);

    // Generate learning trends
    const learningTrends = this.generateLearningTrends(answers);

    // Identify knowledge gaps
    const knowledgeGaps = this.identifyKnowledgeGaps(answers);

    // Generate study recommendations
    const studyRecommendations = this.generateStudyRecommendations(answers, knowledgeGaps);

    // Generate next steps
    const nextSteps = this.generateNextSteps(overallScore, knowledgeGaps);

    return {
      userId,
      assessmentId,
      overallScore,
      categoryBreakdown,
      learningTrends,
      knowledgeGaps,
      studyRecommendations,
      nextSteps,
    };
  }

  private evaluateAnswer(question: Question, userAnswer: any): boolean {
    if (!question.correct_answer) return false;

    switch (question.type) {
      case 'multiple_choice':
        return question.correct_answer.option_ids?.includes(userAnswer);
      case 'multiple_select':
        const correctIds = question.correct_answer.option_ids || [];
        const userIds = Array.isArray(userAnswer) ? userAnswer : [userAnswer];
        return JSON.stringify(correctIds.sort()) === JSON.stringify(userIds.sort());
      case 'true_false':
        return question.correct_answer.option_ids?.includes(userAnswer);
      case 'short_answer':
        return this.evaluateShortAnswer(question.correct_answer.text || '', userAnswer);
      default:
        return false;
    }
  }

  private evaluateShortAnswer(correctAnswer: string, userAnswer: string): boolean {
    // Simple text comparison - can be enhanced with NLP
    const correct = correctAnswer.toLowerCase().trim();
    const user = userAnswer.toLowerCase().trim();
    
    // Exact match
    if (correct === user) return true;
    
    // Keyword matching
    const correctKeywords = correct.split(/\s+/);
    const userKeywords = user.split(/\s+/);
    const matchedKeywords = correctKeywords.filter(kw => userKeywords.includes(kw));
    
    return matchedKeywords.length / correctKeywords.length >= 0.7;
  }

  private generateExplanation(question: Question, userAnswer: any, isCorrect: boolean): string {
    let explanation = question.explanation || '';
    
    if (!isCorrect) {
      explanation += '\n\nCommon mistakes in this area include: ';
      // Add common mistake patterns based on question category
      switch (question.category) {
        case QuestionCategory.ANATOMY:
          explanation += 'confusing similar anatomical structures or their functions.';
          break;
        case QuestionCategory.PHYSIOLOGY:
          explanation += 'misunderstanding physiological processes or their interactions.';
          break;
        case QuestionCategory.PHARMACOLOGY:
          explanation += 'mixing up drug mechanisms or side effects.';
          break;
        default:
          explanation += 'not fully understanding the underlying concepts.';
      }
    }

    return explanation;
  }

  private identifyConceptsToReview(question: Question, isCorrect: boolean): string[] {
    const concepts: string[] = [];
    
    if (!isCorrect) {
      // Add concepts based on question tags and category
      if (question.tags) {
        concepts.push(...question.tags);
      }
      
      // Add category-specific concepts
      switch (question.category) {
        case QuestionCategory.ANATOMY:
          concepts.push('anatomical structures', 'body systems');
          break;
        case QuestionCategory.PHYSIOLOGY:
          concepts.push('physiological processes', 'homeostasis');
          break;
        case QuestionCategory.PHARMACOLOGY:
          concepts.push('drug mechanisms', 'pharmacokinetics');
          break;
      }
    }

    return [...new Set(concepts)]; // Remove duplicates
  }

  private async getRelatedResources(question: Question, isCorrect: boolean): Promise<any[]> {
    const resources: any[] = [];
    
    if (!isCorrect) {
      // Add resources based on question category and tags
      switch (question.category) {
        case QuestionCategory.ANATOMY:
          resources.push({
            type: 'video',
            title: 'Anatomy Review Video',
            url: '/resources/anatomy-review',
            description: 'Visual guide to anatomical structures',
          });
          break;
        case QuestionCategory.PHYSIOLOGY:
          resources.push({
            type: 'article',
            title: 'Physiology Fundamentals',
            url: '/resources/physiology-fundamentals',
            description: 'Core physiological concepts explained',
          });
          break;
      }
    }

    return resources;
  }

  private async analyzeDifficulty(userId: string, question: Question): Promise<any> {
    // Get user's recent performance
    const recentAnswers = await this.answerRepository.find({
      where: { attempt: { user_id: userId } },
      relations: ['attempt'],
      order: { created_at: 'DESC' },
      take: 20,
    });

    const userLevel = this.calculateUserLevel(recentAnswers);
    const questionDifficulty = question.difficulty;

    let recommendation = '';
    if (questionDifficulty === 'hard' && userLevel < 0.7) {
      recommendation = 'Consider reviewing easier material first';
    } else if (questionDifficulty === 'easy' && userLevel > 0.8) {
      recommendation = 'You might benefit from more challenging questions';
    } else {
      recommendation = 'This question is appropriate for your current level';
    }

    return {
      questionDifficulty,
      userPerformanceLevel: userLevel > 0.8 ? 'advanced' : userLevel > 0.6 ? 'intermediate' : 'beginner',
      recommendation,
    };
  }

  private analyzeResponseTime(responseTime: number, question: Question): any {
    // Estimate average time based on question complexity
    const estimatedTime = this.estimateQuestionTime(question);
    const efficiency = responseTime < estimatedTime * 0.7 ? 'fast' : 
                     responseTime > estimatedTime * 1.5 ? 'slow' : 'optimal';

    let suggestion = '';
    switch (efficiency) {
      case 'fast':
        suggestion = 'Good speed! Make sure you\'re reading questions carefully.';
        break;
      case 'slow':
        suggestion = 'Take time to understand, but try to be more decisive.';
        break;
      case 'optimal':
        suggestion = 'Good pacing on this question.';
        break;
    }

    return {
      timeSpent: responseTime,
      averageTime: estimatedTime,
      efficiency,
      suggestion,
    };
  }

  private generateCategoryBreakdown(answers: AssessmentAnswer[]): any[] {
    const categoryMap = new Map();
    
    answers.forEach(answer => {
      const category = answer.question?.category || 'unknown';
      if (!categoryMap.has(category)) {
        categoryMap.set(category, {
          category,
          correct: 0,
          total: 0,
          totalTime: 0,
        });
      }
      
      const data = categoryMap.get(category);
      data.total++;
      if (answer.is_correct) data.correct++;
      data.totalTime += answer.time_spent_seconds || 30;
    });

    return Array.from(categoryMap.values()).map(data => ({
      category: data.category,
      score: (data.correct / data.total) * 100,
      questionsAnswered: data.total,
      averageTime: data.totalTime / data.total,
      strengths: data.score > 80 ? ['Strong understanding'] : [],
      weaknesses: data.score < 60 ? ['Needs improvement'] : [],
    }));
  }

  private generateLearningTrends(answers: AssessmentAnswer[]): any[] {
    // Group answers by date and calculate daily scores
    const dailyScores = new Map();
    
    answers.forEach(answer => {
      const date = answer.created_at.toDateString();
      if (!dailyScores.has(date)) {
        dailyScores.set(date, { correct: 0, total: 0, category: answer.question?.category });
      }
      
      const data = dailyScores.get(date);
      data.total++;
      if (answer.is_correct) data.correct++;
    });

    return Array.from(dailyScores.entries()).map(([date, data]) => ({
      date: new Date(date),
      score: (data.correct / data.total) * 100,
      category: data.category,
    }));
  }

  private identifyKnowledgeGaps(answers: AssessmentAnswer[]): any[] {
    const gaps: any[] = [];
    const categoryPerformance = this.generateCategoryBreakdown(answers);
    
    categoryPerformance.forEach(category => {
      if (category.score < 60) {
        gaps.push({
          topic: category.category,
          severity: category.score < 40 ? 'high' : 'medium',
          recommendedActions: [
            'Review fundamental concepts',
            'Practice more questions in this area',
            'Seek additional resources',
          ],
        });
      }
    });

    return gaps;
  }

  private generateStudyRecommendations(answers: AssessmentAnswer[], knowledgeGaps: any[]): any[] {
    const recommendations: any[] = [];
    
    knowledgeGaps.forEach(gap => {
      recommendations.push({
        priority: gap.severity === 'high' ? 'high' : 'medium',
        topic: gap.topic,
        estimatedStudyTime: gap.severity === 'high' ? 120 : 60, // minutes
        resources: [
          `${gap.topic} fundamentals course`,
          `${gap.topic} practice questions`,
          `${gap.topic} reference materials`,
        ],
      });
    });

    return recommendations;
  }

  private generateNextSteps(overallScore: number, knowledgeGaps: any[]): string[] {
    const steps: string[] = [];
    
    if (overallScore < 60) {
      steps.push('Focus on fundamental concepts before advancing');
      steps.push('Complete remedial coursework in weak areas');
    } else if (overallScore < 80) {
      steps.push('Address specific knowledge gaps identified');
      steps.push('Practice more challenging questions');
    } else {
      steps.push('Excellent work! Consider advanced topics');
      steps.push('Help others to reinforce your knowledge');
    }

    if (knowledgeGaps.length > 0) {
      steps.push(`Priority areas: ${knowledgeGaps.map(g => g.topic).join(', ')}`);
    }

    return steps;
  }

  private calculateUserLevel(answers: AssessmentAnswer[]): number {
    if (answers.length === 0) return 0.5;
    const correct = answers.filter(a => a.is_correct).length;
    return correct / answers.length;
  }

  private estimateQuestionTime(question: Question): number {
    // Base time estimates in seconds
    let baseTime = 30;
    
    switch (question.type) {
      case 'multiple_choice':
        baseTime = 45;
        break;
      case 'multiple_select':
        baseTime = 60;
        break;
      case 'short_answer':
        baseTime = 90;
        break;
      case 'essay':
        baseTime = 300;
        break;
    }

    // Adjust for difficulty
    switch (question.difficulty) {
      case 'easy':
        baseTime *= 0.8;
        break;
      case 'hard':
        baseTime *= 1.5;
        break;
    }

    return baseTime;
  }
}
