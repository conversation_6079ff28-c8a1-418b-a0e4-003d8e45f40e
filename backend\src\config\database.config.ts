import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { join } from 'path';

/**
 * Centralized database configuration factory
 * This is the single source of truth for all database configurations
 */
export default registerAs('database', (): TypeOrmModuleOptions => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Validate required environment variables
  const requiredEnvVars = ['POSTGRES_HOST', 'POSTGRES_USER', 'POSTGRES_PASSWORD', 'POSTGRES_DB'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  return {
    type: 'postgres',
    host: process.env.POSTGRES_HOST,
    port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,

    // Entity and migration paths - standardized
    entities: [join(__dirname, '..', '**', '*.entity.{ts,js}')],
    migrations: [join(__dirname, '..', 'database', 'migrations', '*.{ts,js}')],
    migrationsTableName: 'migrations',

    // Security and performance settings
    synchronize: false, // NEVER enable in production
    migrationsRun: false, // Run migrations manually
    logging: isDevelopment ? ['query', 'error', 'warn'] : ['error'],

    // Connection pool settings
    maxQueryExecutionTime: parseInt(process.env.DATABASE_QUERY_TIMEOUT || '30000', 10),
    acquireTimeout: parseInt(process.env.DATABASE_ACQUIRE_TIMEOUT || '60000', 10),
    timeout: parseInt(process.env.DATABASE_TIMEOUT || '60000', 10),

    // SSL configuration for production
    ssl: isProduction ? {
      rejectUnauthorized: process.env.DATABASE_SSL_REJECT_UNAUTHORIZED !== 'false',
      ca: process.env.DATABASE_SSL_CA,
      cert: process.env.DATABASE_SSL_CERT,
      key: process.env.DATABASE_SSL_KEY,
    } : false,

    // Additional production optimizations
    extra: {
      max: parseInt(process.env.DATABASE_MAX_CONNECTIONS || '10', 10),
      min: parseInt(process.env.DATABASE_MIN_CONNECTIONS || '1', 10),
      idleTimeoutMillis: parseInt(process.env.DATABASE_IDLE_TIMEOUT || '30000', 10),
      connectionTimeoutMillis: parseInt(process.env.DATABASE_CONNECTION_TIMEOUT || '2000', 10),
    },
  };
});
