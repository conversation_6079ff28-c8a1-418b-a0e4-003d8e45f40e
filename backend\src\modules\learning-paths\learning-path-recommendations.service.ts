import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  LearningPath,
  LearningPathCategory,
  LearningPathDifficulty,
} from '../../entities/learning-path.entity';
import { LearningPathProgress } from '../../entities/learning-path-progress.entity';
import { User } from '../../entities/user.entity';
import { CourseEnrollment } from '../../entities/course-enrollment.entity';
import { AssessmentAttempt } from '../../entities/assessment-attempt.entity';
import { CaseAttempt } from '../../entities/case-attempt.entity';
import {
  LearningGoal,
  GoalCategory,
} from '../../entities/learning-goal.entity';

export interface UserLearningProfile {
  user_id: string;
  learning_style:
    | 'visual'
    | 'auditory'
    | 'kinesthetic'
    | 'reading_writing'
    | 'mixed';
  preferred_difficulty: LearningPathDifficulty;
  career_goals: string[];
  study_patterns: {
    preferred_session_duration: number;
    preferred_study_times: string[];
    consistency_score: number;
  };
  performance_metrics: {
    average_assessment_score: number;
    course_completion_rate: number;
    clinical_case_success_rate: number;
    learning_velocity: number;
  };
  interests: LearningPathCategory[];
  weaknesses: string[];
  strengths: string[];
}

export interface RecommendationScore {
  path_id: string;
  score: number;
  reasons: string[];
  confidence: number;
  estimated_completion_time: number;
}

@Injectable()
export class LearningPathRecommendationsService {
  private readonly logger = new Logger(LearningPathRecommendationsService.name);

  constructor(
    @InjectRepository(LearningPath)
    private pathRepository: Repository<LearningPath>,
    @InjectRepository(LearningPathProgress)
    private progressRepository: Repository<LearningPathProgress>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(CourseEnrollment)
    private courseEnrollmentRepository: Repository<CourseEnrollment>,
    @InjectRepository(AssessmentAttempt)
    private assessmentAttemptRepository: Repository<AssessmentAttempt>,
    @InjectRepository(CaseAttempt)
    private clinicalCaseAttemptRepository: Repository<CaseAttempt>,
    @InjectRepository(LearningGoal)
    private goalRepository: Repository<LearningGoal>,
  ) {}

  /**
   * Get personalized learning path recommendations for a user
   */
  async getRecommendations(
    userId: string,
    limit: number = 10,
  ): Promise<RecommendationScore[]> {
    try {
      // Build user learning profile
      const userProfile = await this.buildUserLearningProfile(userId);

      // Get available learning paths (exclude already enrolled)
      const availablePaths = await this.getAvailablePaths(userId);

      // Score each path based on user profile
      const scoredPaths = await Promise.all(
        availablePaths.map((path) => this.scorePath(path, userProfile)),
      );

      // Sort by score and return top recommendations
      return scoredPaths.sort((a, b) => b.score - a.score).slice(0, limit);
    } catch (error) {
      this.logger.error(
        `Error generating recommendations for user ${userId}: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  /**
   * Get recommendations based on similar users (collaborative filtering)
   */
  async getCollaborativeRecommendations(
    userId: string,
    limit: number = 5,
  ): Promise<RecommendationScore[]> {
    try {
      // Find users with similar learning patterns
      const similarUsers = await this.findSimilarUsers(userId);

      // Get paths that similar users have completed successfully
      const pathCompletions = await this.progressRepository
        .createQueryBuilder('progress')
        .leftJoinAndSelect('progress.learning_path', 'path')
        .where('progress.user_id IN (:...userIds)', { userIds: similarUsers })
        .andWhere('progress.status = :status', { status: 'completed' })
        .andWhere('progress.overall_progress_percentage >= :threshold', {
          threshold: 80,
        })
        .getMany();

      // Score paths based on completion success of similar users
      const pathScores = new Map<
        string,
        { score: number; count: number; reasons: string[] }
      >();

      for (const completion of pathCompletions) {
        const pathId = completion.learning_path.id;
        const existing = pathScores.get(pathId) || {
          score: 0,
          count: 0,
          reasons: [],
        };

        existing.score += this.calculateCollaborativeScore(completion);
        existing.count += 1;
        existing.reasons.push(`Completed successfully by similar learners`);

        pathScores.set(pathId, existing);
      }

      // Convert to recommendation scores
      const recommendations: RecommendationScore[] = [];
      for (const [pathId, data] of pathScores.entries()) {
        const path = pathCompletions.find(
          (p) => p.learning_path.id === pathId,
        )?.learning_path;
        if (path) {
          recommendations.push({
            path_id: pathId,
            score: data.score / data.count, // Average score
            reasons: [...new Set(data.reasons)], // Unique reasons
            confidence: Math.min(data.count / 5, 1), // Higher confidence with more completions
            estimated_completion_time: path.estimated_duration_weeks,
          });
        }
      }

      return recommendations.sort((a, b) => b.score - a.score).slice(0, limit);
    } catch (error) {
      this.logger.error(
        `Error generating collaborative recommendations: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  /**
   * Get trending learning paths based on recent enrollments and completions
   */
  async getTrendingPaths(limit: number = 5): Promise<LearningPath[]> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const trendingPaths = await this.progressRepository
        .createQueryBuilder('progress')
        .leftJoinAndSelect('progress.learning_path', 'path')
        .leftJoinAndSelect('path.created_by', 'creator')
        .where('progress.started_at >= :date', { date: thirtyDaysAgo })
        .andWhere('path.status = :status', { status: 'published' })
        .groupBy(
          'path.id, path.title, path.description, path.category, path.difficulty, path.estimated_duration_weeks, creator.id, creator.first_name, creator.last_name',
        )
        .orderBy('COUNT(progress.id)', 'DESC')
        .limit(limit)
        .getMany();

      return trendingPaths.map((p) => p.learning_path);
    } catch (error) {
      this.logger.error(
        `Error getting trending paths: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  /**
   * Build comprehensive user learning profile
   */
  private async buildUserLearningProfile(
    userId: string,
  ): Promise<UserLearningProfile> {
    const user = await this.userRepository.findOne({ where: { id: userId } });

    // Get user's course performance
    const courseEnrollments = await this.courseEnrollmentRepository.find({
      where: { user_id: userId },
      relations: ['course'],
    });

    // Get assessment performance
    const assessmentAttempts = await this.assessmentAttemptRepository.find({
      where: { user_id: userId },
      order: { created_at: 'DESC' },
      take: 50, // Last 50 attempts
    });

    // Get clinical case performance
    const clinicalCaseAttempts = await this.clinicalCaseAttemptRepository.find({
      where: { user_id: userId },
      order: { created_at: 'DESC' },
      take: 50,
    });

    // Get learning goals to understand career aspirations
    const learningGoals = await this.goalRepository.find({
      where: { user_id: userId },
      order: { created_at: 'DESC' },
    });

    // Get learning path history
    const pathHistory = await this.progressRepository.find({
      where: { user_id: userId },
      relations: ['learning_path'],
    });

    // Analyze performance metrics
    const avgAssessmentScore =
      assessmentAttempts.length > 0
        ? assessmentAttempts.reduce(
            (sum, attempt) => sum + (attempt.score || 0),
            0,
          ) / assessmentAttempts.length
        : 0;

    const courseCompletionRate =
      courseEnrollments.length > 0
        ? courseEnrollments.filter((e) => e.status === 'completed').length /
          courseEnrollments.length
        : 0;

    const clinicalCaseSuccessRate =
      clinicalCaseAttempts.length > 0
        ? clinicalCaseAttempts.filter((a) => a.percentage >= 70).length /
          clinicalCaseAttempts.length
        : 0;

    // Determine learning style based on performance patterns
    const learningStyle = this.inferLearningStyle(
      assessmentAttempts,
      clinicalCaseAttempts,
    );

    // Determine preferred difficulty based on completion patterns
    const preferredDifficulty = this.inferPreferredDifficulty(pathHistory);

    // Extract career goals from learning goals and user profile
    const careerGoals = this.extractCareerGoals(learningGoals, user);

    // Analyze study patterns
    const studyPatterns = this.analyzeStudyPatterns(
      pathHistory,
      courseEnrollments,
    );

    // Identify interests based on completed content
    const interests = this.identifyInterests(pathHistory, courseEnrollments);

    // Identify strengths and weaknesses
    const { strengths, weaknesses } = this.analyzeStrengthsWeaknesses(
      assessmentAttempts,
      clinicalCaseAttempts,
      courseEnrollments,
    );

    return {
      user_id: userId,
      learning_style: learningStyle,
      preferred_difficulty: preferredDifficulty,
      career_goals: careerGoals,
      study_patterns: studyPatterns,
      performance_metrics: {
        average_assessment_score: avgAssessmentScore,
        course_completion_rate: courseCompletionRate,
        clinical_case_success_rate: clinicalCaseSuccessRate,
        learning_velocity: this.calculateLearningVelocity(pathHistory),
      },
      interests,
      weaknesses,
      strengths,
    };
  }

  /**
   * Get available learning paths (excluding already enrolled)
   */
  private async getAvailablePaths(userId: string): Promise<LearningPath[]> {
    const enrolledPathIds = await this.progressRepository
      .find({ where: { user_id: userId } })
      .then((progresses) => progresses.map((p) => p.learning_path_id));

    const queryBuilder = this.pathRepository
      .createQueryBuilder('path')
      .leftJoinAndSelect('path.created_by', 'creator')
      .leftJoinAndSelect('path.courses', 'courses')
      .leftJoinAndSelect('path.assessments', 'assessments')
      .leftJoinAndSelect('path.clinical_cases', 'clinical_cases')
      .leftJoinAndSelect('path.milestones', 'milestones')
      .where('path.status = :status', { status: 'published' })
      .andWhere('path.type = :type', { type: 'template' });

    if (enrolledPathIds.length > 0) {
      queryBuilder.andWhere('path.id NOT IN (:...enrolledIds)', {
        enrolledIds: enrolledPathIds,
      });
    }

    return await queryBuilder.getMany();
  }

  /**
   * Score a learning path based on user profile
   */
  private async scorePath(
    path: LearningPath,
    profile: UserLearningProfile,
  ): Promise<RecommendationScore> {
    let score = 0;
    const reasons: string[] = [];

    // Category interest match (30% weight)
    if (profile.interests.includes(path.category)) {
      score += 30;
      reasons.push(
        `Matches your interest in ${path.category.replace('_', ' ')}`,
      );
    }

    // Difficulty preference match (20% weight)
    if (path.difficulty === profile.preferred_difficulty) {
      score += 20;
      reasons.push(`Matches your preferred difficulty level`);
    } else if (
      this.isDifficultyProgression(
        profile.preferred_difficulty,
        path.difficulty,
      )
    ) {
      score += 15;
      reasons.push(`Good progression from your current level`);
    }

    // Career goal alignment (25% weight)
    const careerAlignment = this.calculateCareerAlignment(
      path,
      profile.career_goals,
    );
    score += careerAlignment * 25;
    if (careerAlignment > 0.5) {
      reasons.push(`Aligns with your career goals`);
    }

    // Performance-based recommendations (15% weight)
    const performanceScore = this.calculatePerformanceScore(
      path,
      profile.performance_metrics,
    );
    score += performanceScore * 15;
    if (performanceScore > 0.7) {
      reasons.push(`Good match for your performance level`);
    }

    // Study pattern compatibility (10% weight)
    const patternScore = this.calculatePatternCompatibility(
      path,
      profile.study_patterns,
    );
    score += patternScore * 10;
    if (patternScore > 0.7) {
      reasons.push(`Fits your study schedule and preferences`);
    }

    // Popularity bonus (small weight)
    const popularityScore = Math.min(
      (path.analytics?.total_enrollments || 0) / 100,
      1,
    );
    score += popularityScore * 5;

    // Calculate confidence based on available data
    const confidence = this.calculateConfidence(profile, path);

    return {
      path_id: path.id,
      score: Math.min(score, 100), // Cap at 100
      reasons,
      confidence,
      estimated_completion_time: this.estimateCompletionTime(path, profile),
    };
  }

  /**
   * Find users with similar learning patterns
   */
  private async findSimilarUsers(
    userId: string,
    limit: number = 20,
  ): Promise<string[]> {
    // This is a simplified similarity calculation
    // In a real implementation, you might use more sophisticated ML algorithms

    const userProgress = await this.progressRepository.find({
      where: { user_id: userId },
      relations: ['learning_path'],
    });

    if (userProgress.length === 0) return [];

    const userCategories = userProgress.map((p) => p.learning_path.category);
    const userDifficulties = userProgress.map(
      (p) => p.learning_path.difficulty,
    );

    // Find users who have completed similar paths
    const similarUsers = await this.progressRepository
      .createQueryBuilder('progress')
      .leftJoin('progress.learning_path', 'path')
      .where('progress.user_id != :userId', { userId })
      .andWhere('path.category IN (:...categories)', {
        categories: userCategories,
      })
      .andWhere('path.difficulty IN (:...difficulties)', {
        difficulties: userDifficulties,
      })
      .andWhere('progress.status = :status', { status: 'completed' })
      .groupBy('progress.user_id')
      .having('COUNT(progress.id) >= :minPaths', {
        minPaths: Math.min(2, userProgress.length),
      })
      .orderBy('COUNT(progress.id)', 'DESC')
      .limit(limit)
      .getRawMany();

    return similarUsers.map((u) => u.progress_user_id);
  }

  // Helper methods for profile analysis
  private inferLearningStyle(
    assessments: any[],
    clinicalCases: any[],
  ): UserLearningProfile['learning_style'] {
    // Simplified learning style inference
    // In practice, this would analyze interaction patterns, time spent on different content types, etc.
    return 'mixed'; // Default to mixed for now
  }

  private inferPreferredDifficulty(
    pathHistory: LearningPathProgress[],
  ): LearningPathDifficulty {
    if (pathHistory.length === 0) return LearningPathDifficulty.BEGINNER;

    const completedPaths = pathHistory.filter((p) => p.status === 'completed');
    if (completedPaths.length === 0) return LearningPathDifficulty.BEGINNER;

    // Return the highest difficulty level successfully completed
    const difficulties = completedPaths.map((p) => p.learning_path.difficulty);
    if (difficulties.includes(LearningPathDifficulty.EXPERT))
      return LearningPathDifficulty.EXPERT;
    if (difficulties.includes(LearningPathDifficulty.ADVANCED))
      return LearningPathDifficulty.ADVANCED;
    if (difficulties.includes(LearningPathDifficulty.INTERMEDIATE))
      return LearningPathDifficulty.INTERMEDIATE;
    return LearningPathDifficulty.BEGINNER;
  }

  private extractCareerGoals(goals: LearningGoal[], user: User): string[] {
    const careerGoals: string[] = [];

    // Extract from learning goals
    goals.forEach((goal) => {
      if (goal.category === GoalCategory.CUSTOM) {
        careerGoals.push('specialty_preparation');
      }
      if (goal.title.toLowerCase().includes('usmle')) {
        careerGoals.push('usmle_preparation');
      }
      if (goal.title.toLowerCase().includes('residency')) {
        careerGoals.push('residency_preparation');
      }
    });

    // Extract from user profile if available
    // Note: User profile structure may need to be updated to include career_goals
    // if (user.profile?.career_goals) {
    //   careerGoals.push(...user.profile.career_goals);
    // }

    return [...new Set(careerGoals)]; // Remove duplicates
  }

  private analyzeStudyPatterns(
    pathHistory: LearningPathProgress[],
    courseEnrollments: any[],
  ): UserLearningProfile['study_patterns'] {
    // Analyze study session patterns from progress data
    const totalSessions = pathHistory.reduce(
      (sum, p) => sum + (p.analytics?.study_sessions?.length || 0),
      0,
    );
    const avgSessionDuration =
      totalSessions > 0
        ? pathHistory.reduce(
            (sum, p) =>
              sum +
              (p.analytics?.study_sessions?.reduce(
                (s, session) => s + session.duration_minutes,
                0,
              ) || 0),
            0,
          ) / totalSessions
        : 60; // Default 60 minutes

    return {
      preferred_session_duration: avgSessionDuration,
      preferred_study_times: ['09:00', '14:00', '19:00'], // Default times
      consistency_score: this.calculateConsistencyScore(pathHistory),
    };
  }

  private identifyInterests(
    pathHistory: LearningPathProgress[],
    courseEnrollments: any[],
  ): LearningPathCategory[] {
    const categoryCount = new Map<LearningPathCategory, number>();

    pathHistory.forEach((p) => {
      const category = p.learning_path.category;
      categoryCount.set(category, (categoryCount.get(category) || 0) + 1);
    });

    // Return categories sorted by frequency
    return Array.from(categoryCount.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([category]) => category)
      .slice(0, 3); // Top 3 interests
  }

  private analyzeStrengthsWeaknesses(
    assessments: any[],
    clinicalCases: any[],
    courses: any[],
  ): { strengths: string[]; weaknesses: string[] } {
    // Simplified analysis - in practice, this would be more sophisticated
    const strengths: string[] = [];
    const weaknesses: string[] = [];

    const avgAssessmentScore =
      assessments.length > 0
        ? assessments.reduce((sum, a) => sum + (a.score || 0), 0) /
          assessments.length
        : 0;

    if (avgAssessmentScore >= 85) {
      strengths.push('assessment_performance');
    } else if (avgAssessmentScore < 70) {
      weaknesses.push('assessment_performance');
    }

    const clinicalSuccessRate =
      clinicalCases.length > 0
        ? clinicalCases.filter((c) => c.final_score >= 80).length /
          clinicalCases.length
        : 0;

    if (clinicalSuccessRate >= 0.8) {
      strengths.push('clinical_reasoning');
    } else if (clinicalSuccessRate < 0.6) {
      weaknesses.push('clinical_reasoning');
    }

    return { strengths, weaknesses };
  }

  private calculateLearningVelocity(
    pathHistory: LearningPathProgress[],
  ): number {
    if (pathHistory.length === 0) return 1;

    const completedPaths = pathHistory.filter(
      (p) => p.status === 'completed' && p.started_at && p.completed_at,
    );
    if (completedPaths.length === 0) return 1;

    const avgCompletionTime =
      completedPaths.reduce((sum, p) => {
        const weeks =
          (new Date(p.completed_at).getTime() -
            new Date(p.started_at).getTime()) /
          (1000 * 60 * 60 * 24 * 7);
        return sum + weeks;
      }, 0) / completedPaths.length;

    const avgEstimatedTime =
      completedPaths.reduce(
        (sum, p) => sum + p.learning_path.estimated_duration_weeks,
        0,
      ) / completedPaths.length;

    // Velocity = estimated time / actual time (higher is faster)
    return avgEstimatedTime / avgCompletionTime;
  }

  private calculateConsistencyScore(
    pathHistory: LearningPathProgress[],
  ): number {
    // Simplified consistency calculation
    // In practice, this would analyze study session regularity
    return 0.7; // Default consistency score
  }

  private isDifficultyProgression(
    current: LearningPathDifficulty,
    target: LearningPathDifficulty,
  ): boolean {
    const difficultyOrder = [
      LearningPathDifficulty.BEGINNER,
      LearningPathDifficulty.INTERMEDIATE,
      LearningPathDifficulty.ADVANCED,
      LearningPathDifficulty.EXPERT,
    ];

    const currentIndex = difficultyOrder.indexOf(current);
    const targetIndex = difficultyOrder.indexOf(target);

    return targetIndex === currentIndex + 1; // Next level up
  }

  private calculateCareerAlignment(
    path: LearningPath,
    careerGoals: string[],
  ): number {
    if (careerGoals.length === 0) return 0.5; // Neutral if no goals specified

    let alignment = 0;

    if (
      careerGoals.includes('usmle_preparation') &&
      (path.category === LearningPathCategory.USMLE_STEP1 ||
        path.category === LearningPathCategory.USMLE_STEP2 ||
        path.category === LearningPathCategory.USMLE_STEP3)
    ) {
      alignment += 0.8;
    }

    if (
      careerGoals.includes('specialty_preparation') &&
      path.category === LearningPathCategory.SPECIALTY_PREP
    ) {
      alignment += 0.8;
    }

    if (
      careerGoals.includes('clinical_skills') &&
      path.category === LearningPathCategory.CLINICAL_SKILLS
    ) {
      alignment += 0.7;
    }

    return Math.min(alignment, 1);
  }

  private calculatePerformanceScore(
    path: LearningPath,
    metrics: UserLearningProfile['performance_metrics'],
  ): number {
    // Match path difficulty with user performance
    let score = 0.5; // Base score

    if (
      metrics.average_assessment_score >= 85 &&
      path.difficulty === LearningPathDifficulty.ADVANCED
    ) {
      score = 0.9;
    } else if (
      metrics.average_assessment_score >= 75 &&
      path.difficulty === LearningPathDifficulty.INTERMEDIATE
    ) {
      score = 0.8;
    } else if (
      metrics.average_assessment_score < 70 &&
      path.difficulty === LearningPathDifficulty.BEGINNER
    ) {
      score = 0.8;
    }

    // Adjust based on completion rate
    if (metrics.course_completion_rate >= 0.8) {
      score += 0.1;
    } else if (metrics.course_completion_rate < 0.5) {
      score -= 0.1;
    }

    return Math.max(0, Math.min(1, score));
  }

  private calculatePatternCompatibility(
    path: LearningPath,
    patterns: UserLearningProfile['study_patterns'],
  ): number {
    // Simple compatibility based on estimated time commitment
    const weeklyHours = path.estimated_hours_per_week;
    const userWeeklyCapacity =
      (patterns.preferred_session_duration / 60) *
      7 *
      patterns.consistency_score;

    if (weeklyHours <= userWeeklyCapacity * 1.2) {
      return 0.8;
    } else if (weeklyHours <= userWeeklyCapacity * 1.5) {
      return 0.6;
    } else {
      return 0.3;
    }
  }

  private calculateConfidence(
    profile: UserLearningProfile,
    path: LearningPath,
  ): number {
    let confidence = 0.5; // Base confidence

    // More data = higher confidence
    if (profile.performance_metrics.average_assessment_score > 0)
      confidence += 0.1;
    if (profile.performance_metrics.course_completion_rate > 0)
      confidence += 0.1;
    if (profile.interests.length > 0) confidence += 0.1;
    if (profile.career_goals.length > 0) confidence += 0.1;
    if (profile.strengths.length > 0 || profile.weaknesses.length > 0)
      confidence += 0.1;

    // Path popularity adds confidence
    if (
      path.analytics?.total_enrollments &&
      path.analytics.total_enrollments > 10
    ) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1);
  }

  private estimateCompletionTime(
    path: LearningPath,
    profile: UserLearningProfile,
  ): number {
    const baseTime = path.estimated_duration_weeks;
    const velocity = profile.performance_metrics.learning_velocity;

    return Math.max(1, Math.round(baseTime / velocity));
  }

  private calculateCollaborativeScore(
    completion: LearningPathProgress,
  ): number {
    let score = 50; // Base score

    // Higher score for better completion
    if (completion.overall_progress_percentage >= 90) score += 20;
    else if (completion.overall_progress_percentage >= 80) score += 10;

    // Bonus for faster completion
    if (completion.started_at && completion.completed_at) {
      const actualWeeks =
        (new Date(completion.completed_at).getTime() -
          new Date(completion.started_at).getTime()) /
        (1000 * 60 * 60 * 24 * 7);
      const estimatedWeeks = completion.learning_path.estimated_duration_weeks;

      if (actualWeeks <= estimatedWeeks * 0.8) {
        score += 15; // Completed faster than expected
      }
    }

    return score;
  }
}
