'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { apiService } from '@/services/api';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { SEO } from '@/components/SEO';
import { Brain, Clock, Award, BarChart2 } from 'lucide-react';

interface Quiz {
  id: string;
  title: string;
  description: string;
  duration: number;
  questionCount: number;
  difficulty: 'easy' | 'medium' | 'hard';
  bestScore?: number;
  lastAttempt?: string;
  isCompleted: boolean;
}

export default function CourseQuizPage() {
  const { courseId } = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [courseTitle, setCourseTitle] = useState('');

  useEffect(() => {
    const fetchCourseQuizzes = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const [courseResponse, quizzesResponse] = await Promise.all([
          apiService.get(`/courses/${courseId}`),
          apiService.get(`/courses/${courseId}/quizzes`),
        ]);
        setCourseTitle(courseResponse.data.title);
        setQuizzes(quizzesResponse.data);
      } catch (err: any) {
        setError(err.message || 'Failed to load course quizzes');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourseQuizzes();
  }, [courseId]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'text-green-500';
      case 'medium':
        return 'text-yellow-500';
      case 'hard':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  if (isLoading) {
    return <LoadingSpinner fullScreen />;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-lg">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">Error Loading Quizzes</h2>
            <p className="mt-2 text-sm text-gray-600">{error}</p>
            <div className="mt-5">
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <SEO title={`${courseTitle} - Quizzes`} description={`Course quizzes for ${courseTitle}`} />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {courseTitle} - Quizzes
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Test your knowledge with these quizzes
          </p>
        </div>

        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {quizzes.map(quiz => (
            <div
              key={quiz.id}
              className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"
            >
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Brain className="h-6 w-6 text-indigo-500" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {quiz.title}
                    </h3>
                  </div>
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${getDifficultyColor(
                      quiz.difficulty
                    )} bg-opacity-10`}
                  >
                    {quiz.difficulty}
                  </span>
                </div>

                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">{quiz.description}</p>

                <div className="mt-4 space-y-2">
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <Clock className="h-4 w-4 mr-1" />
                    {quiz.duration} minutes
                  </div>
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <BarChart2 className="h-4 w-4 mr-1" />
                    {quiz.questionCount} questions
                  </div>
                  {quiz.bestScore && (
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <Award className="h-4 w-4 mr-1" />
                      Best Score: {quiz.bestScore}%
                    </div>
                  )}
                </div>

                <div className="mt-6">
                  <button
                    onClick={() => router.push(`/quiz/${quiz.id}`)}
                    className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    {quiz.isCompleted ? 'Retake Quiz' : 'Start Quiz'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {quizzes.length === 0 && (
          <div className="text-center py-12">
            <Brain className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              No quizzes available
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Check back later for new quizzes
            </p>
          </div>
        )}
      </div>
    </ErrorBoundary>
  );
}
