'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaShieldAlt, FaMobile, FaEnvelope, FaHistory } from 'react-icons/fa';

interface ActiveSession {
  id: string;
  device: string;
  deviceType: string;
  location: string;
  lastActive: string;
  lastAccessed: string;
  ipAddress: string;
  current?: boolean;
}

interface SecurityEvent {
  id: string;
  type: string;
  eventType: string;
  description: string;
  timestamp: string;
  ipAddress: string;
}

export default function SecuritySettingsPage() {
  const { user } = useAuth();
  const [settings, setSettings] = useState({
    twoFactorEnabled: false,
    emailNotificationsEnabled: true,
    securityQuestions: [],
  });
  const [activeSessions, setActiveSessions] = useState<ActiveSession[]>([]);
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [showQRCode, setShowQRCode] = useState(false);
  const [qrCodeData, setQRCodeData] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    loadSecuritySettings();
    loadActiveSessions();
    loadSecurityEvents();
  }, []);

  const loadSecuritySettings = async () => {
    try {
      const response = await fetch('/api/security/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      setError('Failed to load security settings');
    }
  };

  const loadActiveSessions = async () => {
    try {
      const response = await fetch('/api/security/sessions');
      if (response.ok) {
        const data = await response.json();
        setActiveSessions(data);
      }
    } catch (error) {
      setError('Failed to load active sessions');
    }
  };

  const loadSecurityEvents = async () => {
    try {
      const response = await fetch('/api/security/events');
      if (response.ok) {
        const data = await response.json();
        setSecurityEvents(data);
      }
    } catch (error) {
      setError('Failed to load security events');
    }
  };

  const setup2FA = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/security/2fa/setup');
      if (response.ok) {
        const { qrCode } = await response.json();
        setQRCodeData(qrCode);
        setShowQRCode(true);
      }
    } catch (error) {
      setError('Failed to setup 2FA');
    } finally {
      setLoading(false);
    }
  };

  const verify2FA = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/security/2fa/verify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token: verificationCode }),
      });

      if (response.ok) {
        setSettings(prev => ({ ...prev, twoFactorEnabled: true }));
        setShowQRCode(false);
        setSuccess('Two-factor authentication enabled successfully');
      } else {
        setError('Invalid verification code');
      }
    } catch (error) {
      setError('Failed to verify 2FA code');
    } finally {
      setLoading(false);
    }
  };

  const disable2FA = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/security/2fa/disable', {
        method: 'PUT',
      });

      if (response.ok) {
        setSettings(prev => ({ ...prev, twoFactorEnabled: false }));
        setSuccess('Two-factor authentication disabled');
      }
    } catch (error) {
      setError('Failed to disable 2FA');
    } finally {
      setLoading(false);
    }
  };

  const revokeSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/security/sessions/${sessionId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setActiveSessions(prev => prev.filter(session => session.id !== sessionId));
        setSuccess('Session revoked successfully');
      }
    } catch (error) {
      setError('Failed to revoke session');
    }
  };

  const updateNotificationSettings = async (enabled: boolean) => {
    try {
      const response = await fetch('/api/security/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emailNotificationsEnabled: enabled }),
      });

      if (response.ok) {
        setSettings(prev => ({ ...prev, emailNotificationsEnabled: enabled }));
        setSuccess('Notification settings updated');
      }
    } catch (error) {
      setError('Failed to update notification settings');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Security Settings</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Two-Factor Authentication */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center mb-4">
            <FaKey className="text-blue-500 mr-2" />
            <h2 className="text-xl font-semibold">Two-Factor Authentication</h2>
          </div>
          {settings.twoFactorEnabled ? (
            <div>
              <p className="text-green-600 mb-4">✓ Two-factor authentication is enabled</p>
              <button
                onClick={disable2FA}
                disabled={loading}
                className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
              >
                Disable 2FA
              </button>
            </div>
          ) : (
            <div>
              <p className="mb-4">Enable two-factor authentication for enhanced security</p>
              <button
                onClick={setup2FA}
                disabled={loading}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Set up 2FA
              </button>
            </div>
          )}

          {showQRCode && (
            <div className="mt-4">
              <img src={qrCodeData} alt="2FA QR Code" className="mb-4" />
              <input
                type="text"
                value={verificationCode}
                onChange={e => setVerificationCode(e.target.value)}
                placeholder="Enter verification code"
                className="border p-2 rounded mb-2 w-full"
              />
              <button
                onClick={verify2FA}
                disabled={loading}
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
              >
                Verify Code
              </button>
            </div>
          )}
        </div>

        {/* Active Sessions */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center mb-4">
            <FaMobile className="text-blue-500 mr-2" />
            <h2 className="text-xl font-semibold">Active Sessions</h2>
          </div>
          <div className="space-y-4">
            {activeSessions.map(session => (
              <div key={session.id} className="flex items-center justify-between border-b pb-2">
                <div>
                  <p className="font-medium">{session.deviceType}</p>
                  <p className="text-sm text-gray-500">{session.ipAddress}</p>
                  <p className="text-xs text-gray-400">
                    Last active: {new Date(session.lastAccessed).toLocaleString()}
                  </p>
                </div>
                <button
                  onClick={() => revokeSession(session.id)}
                  className="text-red-500 hover:text-red-600"
                >
                  Revoke
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Email Notifications */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center mb-4">
            <FaEnvelope className="text-blue-500 mr-2" />
            <h2 className="text-xl font-semibold">Security Notifications</h2>
          </div>
          <div className="flex items-center">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.emailNotificationsEnabled}
                onChange={e => updateNotificationSettings(e.target.checked)}
                className="form-checkbox h-5 w-5 text-blue-500"
              />
              <span className="ml-2">Enable email notifications for security events</span>
            </label>
          </div>
        </div>

        {/* Recent Security Events */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center mb-4">
            <FaHistory className="text-blue-500 mr-2" />
            <h2 className="text-xl font-semibold">Recent Security Events</h2>
          </div>
          <div className="space-y-4">
            {securityEvents.map(event => (
              <div key={event.id} className="border-b pb-2">
                <p className="font-medium">{event.eventType}</p>
                <p className="text-sm text-gray-500">{event.ipAddress}</p>
                <p className="text-xs text-gray-400">
                  {new Date(event.timestamp).toLocaleString()}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
