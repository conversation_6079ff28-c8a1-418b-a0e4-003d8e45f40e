const { Client } = require('pg');
const axios = require('axios');

async function testDatabaseAndAPI() {
  console.log('🔍 Testing Database Connection and API Endpoints...\n');

  // Test database connection
  const client = new Client({
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT) || 5432,
    user: process.env.POSTGRES_USER || 'medical',
    password: process.env.POSTGRES_PASSWORD || 'AU110s/6081/2021MT',
    database: process.env.POSTGRES_DB || 'medical_tracker',
  });

  try {
    console.log('📊 Testing Database Connection...');
    await client.connect();
    console.log('✅ Database connection successful');

    // Check if tables exist
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('users', 'learning_paths', 'learning_goals')
      ORDER BY table_name;
    `);
    
    console.log('📋 Existing tables:', tablesResult.rows.map(r => r.table_name));

    // Create sample data if tables exist
    if (tablesResult.rows.length > 0) {
      console.log('\n📝 Creating sample data...');
      
      // Create a test user
      try {
        await client.query(`
          INSERT INTO users (id, email, username, name, first_name, last_name, password_hash, role, email_verified)
          VALUES (
            gen_random_uuid(),
            '<EMAIL>',
            'testuser',
            'Test User',
            'Test',
            'User',
            '$2b$10$example.hash.here',
            'student',
            true
          )
          ON CONFLICT (email) DO NOTHING;
        `);
        console.log('✅ Test user created');
      } catch (error) {
        console.log('ℹ️ User creation skipped:', error.message);
      }

      // Check users
      const usersResult = await client.query('SELECT id, email, username FROM users LIMIT 5');
      console.log('👥 Users in database:', usersResult.rows.length);
    }

    await client.end();

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
  }

  // Test API endpoints
  console.log('\n🌐 Testing API Endpoints...');
  
  const baseURL = 'http://localhost:3002';
  const endpoints = [
    '/health',
    '/learning-paths',
    '/learning-goals'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${baseURL}${endpoint}...`);
      const response = await axios.get(`${baseURL}${endpoint}`, {
        timeout: 5000,
        headers: {
          'Accept': 'application/json'
        }
      });
      console.log(`✅ ${endpoint}: ${response.status} - ${JSON.stringify(response.data).substring(0, 100)}...`);
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${endpoint}: Connection refused - Backend not running`);
      } else if (error.response) {
        console.log(`⚠️ ${endpoint}: ${error.response.status} - ${error.response.statusText}`);
      } else {
        console.log(`❌ ${endpoint}: ${error.message}`);
      }
    }
  }

  console.log('\n🏁 Test completed!');
}

// Run the test
testDatabaseAndAPI().catch(console.error);
