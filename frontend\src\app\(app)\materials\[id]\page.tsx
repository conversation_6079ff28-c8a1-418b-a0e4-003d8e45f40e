'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import useRequireAuth from '@/hooks/useRequireAuth';
import { ArrowLeft, Download, File, Share, Trash } from 'lucide-react';
import materialService from '@/services/materialService';

interface Material {
  id: string;
  title: string;
  description?: string;
  type?: string;
  author?: string;
  uploadDate?: string;
  size?: string;
  url?: string;
  content?: string;
}

export default function MaterialViewPage({ params }: { params: Promise<{ id: string }> }) {
  const { isLoading } = useRequireAuth();
  const router = useRouter();
  const [material, setMaterial] = useState<Material | null>(null);
  const [isLoadingMaterial, setIsLoadingMaterial] = useState(true);
  const [error, setError] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [materialId, setMaterialId] = useState<string>('');

  useEffect(() => {
    const initializeParams = async () => {
      const resolvedParams = await params;
      setMaterialId(resolvedParams.id);
    };
    initializeParams();
  }, [params]);

  useEffect(() => {
    if (!materialId) return;

    const fetchMaterial = async () => {
      try {
        setIsLoadingMaterial(true);

        try {
          // Try to fetch from the real API
          const data = await materialService.getMaterialById(materialId);
          setMaterial(data);
        } catch (apiError) {
          console.warn('API fetch failed, using fallback:', apiError);

          // Fallback to mock data
          // In a real app, you would handle this differently
          const mockMaterial: Material = {
            id: materialId,
            title: 'Sample Study Material',
            description: 'This is a sample study material for demonstration purposes.',
            type: 'pdf',
            author: 'Dr. John Doe',
            uploadDate: new Date().toISOString(),
            size: '2.4 MB',
            url: '/sample.pdf',
            content: `
              # Sample Study Notes
              
              ## Introduction
              
              These are sample study notes for demonstration purposes. In a real application, this would be the actual content of your notes.
              
              ## Key Points
              
              1. First important point about the topic
              2. Second important point about the topic
              3. Third important point about the topic
              
              ## Summary
              
              This is a summary of the study material. It covers the main points and provides a concise overview of the topic.
            `,
          };

          setMaterial(mockMaterial);
        }
      } catch (error) {
        console.error('Error fetching material:', error);
        setError('Failed to load material. Please try again.');
      } finally {
        setIsLoadingMaterial(false);
      }
    };

    fetchMaterial();
  }, [materialId]);

  const handleDelete = async () => {
    if (!material) return;

    try {
      await materialService.deleteMaterial(material.id);
      router.push('/materials');
    } catch (error) {
      console.error('Error deleting material:', error);
      setError('Failed to delete material. Please try again.');
      setShowDeleteConfirm(false);
    }
  };

  const handleDownload = () => {
    if (!material?.url) return;

    // Create a temporary link element
    const link = document.createElement('a');
    link.href = material.url;
    link.download = `${material.title}.${material.type}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleShare = () => {
    if (!material) return;

    // Use the Web Share API if available
    if (navigator.share) {
      navigator
        .share({
          title: material.title,
          text: material.description,
          url: window.location.href,
        })
        .catch(error => {
          console.error('Error sharing:', error);
        });
    } else {
      // Fallback to copying the URL to clipboard
      navigator.clipboard
        .writeText(window.location.href)
        .then(() => {
          alert('Link copied to clipboard!');
        })
        .catch(error => {
          console.error('Error copying to clipboard:', error);
        });
    }
  };

  const renderContent = () => {
    if (!material) return null;

    // For text-based content
    if (material.content) {
      return (
        <div className="prose max-w-none">
          <pre className="whitespace-pre-wrap">{material.content}</pre>
        </div>
      );
    }

    // For PDFs
    if (material.type === 'pdf' && material.url) {
      return (
        <div className="w-full h-[70vh]">
          <iframe src={material.url} className="w-full h-full border-0" title={material.title} />
        </div>
      );
    }

    // For images
    if (['jpg', 'jpeg', 'png'].includes(material.type || '') && material.url) {
      return (
        <div className="flex justify-center">
          <img
            src={material.url}
            alt={material.title}
            className="max-w-full max-h-[70vh] object-contain"
          />
        </div>
      );
    }

    // For other file types
    return (
      <div className="text-center py-10">
        <File className="h-16 w-16 mx-auto text-gray-400" />
        <h3 className="mt-2 text-lg font-medium text-gray-900">Preview not available</h3>
        <p className="mt-1 text-sm text-gray-500">
          This file type cannot be previewed. Please download the file to view it.
        </p>
        <Button variant="default" size="sm" className="mt-4" onClick={handleDownload}>
          <Download className="h-4 w-4 mr-2" />
          Download
        </Button>
      </div>
    );
  };

  if (isLoading || isLoadingMaterial) {
    return (
      <div className="flex items-center justify-center min-h-[80vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <div className="text-red-500 mb-4">{error}</div>
        <Button variant="secondary" onClick={() => router.push('/materials')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Materials
        </Button>
      </div>
    );
  }

  if (!material) {
    return (
      <div className="text-center py-10">
        <h3 className="text-lg font-medium text-gray-900">Material not found</h3>
        <Button variant="secondary" onClick={() => router.push('/materials')} className="mt-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Materials
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="secondary" onClick={() => router.push('/materials')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>

        <div className="flex space-x-2">
          <Button variant="secondary" onClick={handleShare}>
            <Share className="h-4 w-4 mr-2" />
            Share
          </Button>

          <Button variant="secondary" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>

          <Button variant="destructive" onClick={() => setShowDeleteConfirm(true)}>
            <Trash className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{material.title}</CardTitle>
          {material.description && <p className="text-gray-500 mt-1">{material.description}</p>}
          <div className="flex flex-wrap gap-4 text-sm text-gray-500 mt-2">
            {material.author && <div>Author: {material.author}</div>}
            {material.uploadDate && (
              <div>Uploaded: {new Date(material.uploadDate).toLocaleDateString()}</div>
            )}
            {material.size && <div>Size: {material.size}</div>}
            {material.type && <div>Type: {material.type.toUpperCase()}</div>}
          </div>
        </CardHeader>
        <CardContent>{renderContent()}</CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full">
            <h3 className="text-lg font-medium mb-4">Delete Material</h3>
            <p className="text-gray-500 mb-4">
              Are you sure you want to delete "{material.title}"? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-2">
              <Button variant="secondary" onClick={() => setShowDeleteConfirm(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDelete}>
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
