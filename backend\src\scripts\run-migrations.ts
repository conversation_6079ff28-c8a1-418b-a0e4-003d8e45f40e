#!/usr/bin/env ts-node
import { AppDataSource } from '../database/datasource';
import { config } from 'dotenv';
import * as path from 'path';

// Load environment variables from .env.production
config({ path: path.join(__dirname, '../../.env.production') });

async function runMigrations() {
  try {
    await AppDataSource.initialize();
    console.log('Database connection initialized');

    await AppDataSource.runMigrations();
    console.log('Migrations completed successfully');

    await AppDataSource.destroy();
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
    process.exit(1);
  }
}

runMigrations();
