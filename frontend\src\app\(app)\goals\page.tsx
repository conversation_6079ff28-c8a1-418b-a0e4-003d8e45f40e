'use client';

import React, { useState } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { GoalsManagementInterface } from '@/components/goals/GoalsManagementInterface';
import { GoalCreationWizard } from '@/components/goals/GoalCreationWizard';

export default function GoalsPage() {
  const [showCreateModal, setShowCreateModal] = useState(false);

  const handleGoalCreated = () => {
    // Refresh the goals list
    window.location.reload();
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <GoalsManagementInterface />
        
        <GoalCreationWizard
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onGoalCreated={handleGoalCreated}
        />
      </div>
    </ProtectedRoute>
  );
}
