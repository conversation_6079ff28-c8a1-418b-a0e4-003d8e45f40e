// app/layout.tsx
import './globals.css';
import { ReactNode } from 'react';
import { Providers } from './providers';
import { Toaster } from 'react-hot-toast';
import { SyncStatusBanner } from '@/components/SyncStatusBanner';

export const metadata = {
  metadataBase: new URL('http://localhost:3000'),
  title: 'MedTrack Hub | Medical Learning Platform',
  description: 'Track your medical study progress, assess your readiness, and stay ahead.',
  keywords: [
    'MedTrack Hub',
    'MedTrack',
    'medical education',
    'quiz app',
    'study tracking',
    'medical students',
  ],
  openGraph: {
    title: 'MedTrack Hub | Medical Learning Platform',
    description: 'Track your medical study progress, assess your readiness, and stay ahead.',
    url: 'https://medtrackhub.com', // replace with actual domain
    siteName: 'MedTrack Hub',
    locale: 'en_US',
    type: 'website',
    images: [
      {
        url: '/og-image.png', // replace with your actual OG image
        width: 1200,
        height: 630,
        alt: 'MedTrack Hub Banner',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'MedTrack Hub | Medical Learning Platform',
    description: 'Track your medical study progress, assess your readiness, and stay ahead.',
    images: ['/og-image.png'],
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-32x32.png',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#1e40af',
};

interface RootLayoutProps {
  children: ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en">
      <body>
        <Providers>
          <Toaster position="top-right" />
          <SyncStatusBanner />
          {children}
        </Providers>
      </body>
    </html>
  );
}
